<?php
/**
 * Niushop商城系统 - 团队十年电商经验汇集巨献!
 * =========================================================
 * Copy right 2019-2029 上海牛之云网络科技有限公司, 保留所有权利。
 * ----------------------------------------------
 * 官方网址: https://www.niushop.com.cn
 * 这不是一个自由软件！您只能在不用于商业目的的前提下对程序代码进行修改和使用。
 * 任何企业和个人不允许对程序代码以任何形式任何目的再发布。
 * =========================================================
 */

namespace app\shop\controller;

use app\model\order\Config as ConfigModel;
use app\model\order\OrderCommon as OrderCommonModel;
use app\model\order\OrderCreate as OrderCreateModel;
use app\model\system\Address as AddressModel;
use app\model\system\User as UserModel;
use app\model\order\Order as OrderModel;
use app\model\goods\Goods as GoodsModel;
use app\model\order\OrderCommon;
use app\model\order\OrderExport;
use think\facade\Config;
use app\model\system\Promotion as PromotionModel;
use phpoffice\phpexcel\Classes\PHPExcel;
use phpoffice\phpexcel\Classes\PHPExcel\Writer\Excel2007;
use think\facade\Db;

/**
 * 订单
 * Class Order
 * @package app\shop\controller
 */
class Order extends BaseShop
{

    public function __construct()
    {
        //执行父类构造函数
        parent::__construct();

    }
    /**
     * 处方模板
     */
    public function orderattr(){
        $class_id   = input('class_id', '0');
        $attr_class_info = model('order_attr_class')->getInfo([['class_id', '=', $class_id]], '*');
        $goods_model = new GoodsModel();
        $goods_sku_list                             = $goods_model->getGoodsSkuList([['sku_id', 'in', $attr_class_info['sku_ids']], ['site_id', '=', $this->site_id]], 'sku_id,sku_name,price,stock,sku_image,goods_id,goods_class_name', 'price asc');
        $goods_sku = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            $goods_sku[$vo['sku_id']] = $vo;
        }
        $goods_name = json_decode($attr_class_info['goods_name'],true);
        $goods_num  = json_decode($attr_class_info['goods_num'],true);
        $goods_list = [];
        foreach ($goods_name as $key=>$vo){
            $goods = explode('_',$vo);
            $goods_detail = $goods_sku[$goods[1]];
            $goods_detail['num'] = $goods_num[$key];
            $goods_list[$goods[0]][$goods[1]]=$goods_detail;
        }
        //按产品生成的模板
        $sku_ids = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){

            $sku_ids[$vo['sku_id']]=$vo;
            $sku_ids[$vo['sku_id']]['earlymoring'] = isset($goods_list['earlymoring'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['earlymoring_s'] = isset($goods_list['earlymoring'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['earlymoring_num'] = isset($goods_list['earlymoring'][$vo['sku_id']])?$goods_list['earlymoring'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['moning'] = isset($goods_list['moning'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['moning_s'] = isset($goods_list['moning'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['moning_num'] = isset($goods_list['moning'][$vo['sku_id']])?$goods_list['moning'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['aftnoon'] = isset($goods_list['aftnoon'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['aftnoon_s'] = isset($goods_list['aftnoon'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['aftnoon_num'] = isset($goods_list['aftnoon'][$vo['sku_id']])?$goods_list['aftnoon'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['canjian'] = isset($goods_list['canjian'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['canjian_s'] = isset($goods_list['canjian'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['canjian_num'] = isset($goods_list['canjian'][$vo['sku_id']])?$goods_list['canjian'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['night'] = isset($goods_list['night'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['night_s'] = isset($goods_list['night'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['night_num'] = isset($goods_list['night'][$vo['sku_id']])?$goods_list['night'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['evening'] = isset($goods_list['evening'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['evening_s'] = isset($goods_list['evening'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['evening_num'] = isset($goods_list['evening'][$vo['sku_id']])?$goods_list['evening'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['teshu'] = isset($goods_list['teshu'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['teshu_s'] = isset($goods_list['teshu'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['teshu_num'] = isset($goods_list['teshu'][$vo['sku_id']])?$goods_list['teshu'][$vo['sku_id']]['num']:1;

        }
        return [
            'code' => 0,
            'message' => '成功',
            'earlymoning' => isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[],
            'moning' => isset($goods_list['moning'])?$goods_list['moning']:[],
            'aftnoon' => isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[],
            'canjian' => isset($goods_list['canjian'])?$goods_list['canjian']:[],
            'night' => isset($goods_list['night'])?$goods_list['night']:[],
            'evening' => isset($goods_list['evening'])?$goods_list['evening']:[],
            'teshu' => isset($goods_list['teshu'])?$goods_list['teshu']:[],
            'attr_class_info' => $attr_class_info,
            'sku_ids' => $sku_ids
        ];
    }

    //订单模版
    public function ordera(){
        $order_id   = input('order_id', '0');

        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];
        $this->assign("order_detail", $order_detail);

        $attr_class_info = model('order_attr')->getInfo([['order_id', '=', $order_detail['out_trade_no']]], '*');

        $goods_model = new GoodsModel();
        $goods_sku_list                             = $goods_model->getGoodsSkuList([['sku_id', 'in', $attr_class_info['sku_ids']], ['site_id', '=', $this->site_id]], 'sku_id,sku_name,price,stock,sku_image,goods_id,goods_class_name', 'price asc');
        $goods_sku = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            $goods_sku[$vo['sku_id']] = $vo;
        }
        $goods_name = json_decode($attr_class_info['goods_name'],true);
        $goods_num  = json_decode($attr_class_info['goods_num'],true);
        $goods_list = [];
        foreach ($goods_name as $key=>$vo){
            $goods = explode('_',$vo);
            $goods_detail = $goods_sku[$goods[1]];
            $goods_detail['num'] = $goods_num[$key];
            $goods_list[$goods[0]][$goods[1]]=$goods_detail;
        }
        //按产品生成的模板
        $sku_ids = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){

            $sku_ids[$vo['sku_id']]=$vo;
            $sku_ids[$vo['sku_id']]['earlymoring'] = isset($goods_list['earlymoring'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['earlymoring_s'] = isset($goods_list['earlymoring'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['earlymoring_num'] = isset($goods_list['earlymoring'][$vo['sku_id']])?$goods_list['earlymoring'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['moning'] = isset($goods_list['moning'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['moning_s'] = isset($goods_list['moning'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['moning_num'] = isset($goods_list['moning'][$vo['sku_id']])?$goods_list['moning'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['aftnoon'] = isset($goods_list['aftnoon'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['aftnoon_s'] = isset($goods_list['aftnoon'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['aftnoon_num'] = isset($goods_list['aftnoon'][$vo['sku_id']])?$goods_list['aftnoon'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['canjian'] = isset($goods_list['canjian'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['canjian_s'] = isset($goods_list['canjian'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['canjian_num'] = isset($goods_list['canjian'][$vo['sku_id']])?$goods_list['canjian'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['night'] = isset($goods_list['night'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['night_s'] = isset($goods_list['night'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['night_num'] = isset($goods_list['night'][$vo['sku_id']])?$goods_list['night'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['evening'] = isset($goods_list['evening'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['evening_s'] = isset($goods_list['evening'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['evening_num'] = isset($goods_list['evening'][$vo['sku_id']])?$goods_list['evening'][$vo['sku_id']]['num']:1;
            $sku_ids[$vo['sku_id']]['teshu'] = isset($goods_list['teshu'][$vo['sku_id']])?'checked':'';
            $sku_ids[$vo['sku_id']]['teshu_s'] = isset($goods_list['teshu'][$vo['sku_id']])?'visible':'hidden';
            $sku_ids[$vo['sku_id']]['teshu_num'] = isset($goods_list['teshu'][$vo['sku_id']])?$goods_list['teshu'][$vo['sku_id']]['num']:1;

        }
        //按原来产品顺序进行排序
        $sku_ids_result = [];
        $sku_ids_result_t = [];
        $sku_ids_sort = explode(',',$attr_class_info['sku_ids']);
        foreach ($sku_ids_sort as $key=>$vo){
            if (isset($sku_ids[$vo]) && !in_array($vo,$sku_ids_result_t)){
                $sku_ids_result[]=$sku_ids[$vo];
                $sku_ids_result_t[]=$vo;
            }
        }
        return [
            'code' => 0,
            'message' => '成功',
            'earlymoning' => isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[],
            'moning' => isset($goods_list['moning'])?$goods_list['moning']:[],
            'aftnoon' => isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[],
            'canjian' => isset($goods_list['canjian'])?$goods_list['canjian']:[],
            'night' => isset($goods_list['night'])?$goods_list['night']:[],
            'evening' => isset($goods_list['evening'])?$goods_list['evening']:[],
            'teshu' => isset($goods_list['teshu'])?$goods_list['teshu']:[],
            'attr_class_info' => $attr_class_info,
            'sku_ids' => $sku_ids_result,
            'day'=>$attr_class_info['day'],
            'price'=>$order_detail['goods_money'],
        ];
    }

    /**
     * 生成订单
     */
    public function manual(){
        if (request()->isAjax()) {
            //判断下单用户
            $member_id = input('member_id', 0);
            if (!$member_id){
                $user = model('user')->getInfo([['uid', "=", $this->uid]]);
                $member_id = $user['member_id'];
            }
            //生成后台用户收货地址
            $addressname = input('addressname', '');
            $addressmobile = input('addressmobile', '');
            $address = input('address', '');
            $province_id = input('province_id', '');
            $city_id = input('city_id', '');
            $district_id = input('district_id', '');
            $day = input('day', 0);
            $fenji = input('fenji', 0);
            $ccheck = input('ccheck', 0);
            $c_fenji = input('c_fenji', 0);
            $service_money = input('service_money', '');
            if (!$ccheck){
                return error('-1','请选择是否二次查验');
            }

            if (!$c_fenji && $fenji==1){
                return error('-1','请选择粉剂是否分装');
            }

            if ($day<1){
                return error('-1','疗程天数不能小于1天');
            }
            if (!is_numeric($day)||strpos($day,".")!==false){
                return error('-1','疗程天数必须是整数');
            }
            if ($addressname&&$addressmobile&&$address&&$province_id&&$city_id&&$district_id){
            }else{
                return error('-1','请填写完整收货地址');
            }
            //健康营养师以及干预重点
            $jiankangguanli = input('jiankangguanli', ' ');
            $zhongdian = input('zhongdian', ' ');
            /*if ($jiankangguanli){
            }else{
                return error('-1','请填写健康营养师名称');
            }
            if ($zhongdian){
            }else{
                return error('-1','请填写本期营养素干预重点');
            }*/
            $member_address = model("member_address")->getInfo([['member_id', '=', $member_id], ['is_default', '=', 1]], 'id');
            if (!empty($member_address)) {
                $res = model("member_address")->update(['name' =>$addressname,'mobile'=>$addressmobile,'address'=>$address,'telephone'=>$addressmobile,'province_id'=>$province_id,'city_id'=>$city_id,'district_id'=>$district_id], [['id', '=', $member_address['id']]]);
            } else {
                $res = model("member_address")->add(['member_id'=>$member_id,'site_id'=>1,'is_default'=>1,'name' =>$addressname,'mobile'=>$addressmobile,'address'=>$address,'telephone'=>$addressmobile,'province_id'=>$province_id,'city_id'=>$city_id,'district_id'=>$district_id]);
            }

            //生成购物车,重新整理处方商品信息
            model("goods_cart")->delete([['member_id', '=', $member_id]]);
            $sku_ids = input('sku_ids', '');

            $goods_num = input('goods_nums',[]);
            $goods_name = input('goods_name',[]);
            //特殊产品
            $goods_t_num = input('goods_t_nums',[]);
            $goods_t_name = input('goods_t_name',[]);
            $sku_ids_t_a = [];
            $sku_ids_a = [];
            $service_n = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                $sku_ids_a[$ivv[1]]=(isset($sku_ids_a[$ivv[1]])?$sku_ids_a[$ivv[1]]:0)+$goods_num[$item];
                //外包单
                $service_n[$ivv[0]][]=$ivv[1];
            }
            //特殊产品
            foreach ($goods_t_name as $item=>$iv){
                $ivv = explode('_',$iv);
                $sku_ids_t_a[$ivv[1]]=(isset($sku_ids_t_a[$ivv[1]])?$sku_ids_t_a[$ivv[1]]:0)+$goods_t_num[$item];
            }
            if (!$goods_num && !$goods_t_num){
                return error('-1','请选择处方商品');
            }
            $sku_ids_new = $sku_ids_a;

            //判断商品是否还有库存，是否已下架
            $alias       = 'og';
            $join        = [
                [
                    'goods o',
                    'o.goods_id = og.goods_id',
                    'left'
                ]
            ];
            $goodslist = model('goods_sku')->getList([['og.sku_id','in',array_keys($sku_ids_new)]], 'og.*,o.label_id', 'og.sku_id desc', $alias, $join);
            $goodslist_t = model('goods_sku')->getList([['og.sku_id','in',array_keys($sku_ids_t_a)]], 'og.*,o.label_id', 'og.sku_id desc', $alias, $join);
            $goodslist_detail = [];
            $fenji_t = 0;
            //粉剂，液体
            $fen_ye = [];
            $fen_ye_detail = [];
            $fen_ye_total = 0;
            $guding_detail = [];
            $guding_total = 0;
            foreach ($goodslist as $key =>$vo){
                $goodslist_detail[$vo['sku_id']] = $vo;
                if ($vo['stock']<1 || $vo['goods_state']==0){
                    return error('-1',$vo['sku_name'].'产品已下架');
                }
                //判断粉剂桶数
                if($vo['label_id']==1){
                    $fenji_t = $fenji_t + $sku_ids_new[$vo['sku_id']];
                    //判断粉剂数量
                    if ($sku_ids_new[$vo['sku_id']]*$day<30 || $sku_ids_new[$vo['sku_id']]*$day%30!=0){
                    //    return error('-1',$vo['sku_name'].'产品属于粉剂，订单数量须是30的倍数');
                    }
                    //判断分装数量是否合适
                    if ($c_fenji==2&&$sku_ids_new[$vo['sku_id']]*$day%$vo['daizhuang']!=0){
                    //    return error('-1',$vo['sku_name'].'一桶有'.$vo['daizhuang'].'袋，处方不分装的情况每天的用量须是'.$vo['daizhuang'].'的倍数');
                    }
                }
                if($vo['label_id']==2){
                    //判断粉剂数量
                    if ($sku_ids_new[$vo['sku_id']]*$day<30 || $sku_ids_new[$vo['sku_id']]*$day%30!=0){
                    //    return error('-1',$vo['sku_name'].'产品属于粉剂，订单数量须是30的倍数');
                    }
                }
                //判断粉剂液体
                if($vo['label_id']==1||$vo['label_id']==2){
                    $fen_ye[] = $vo['sku_id'];
                    $vo['fen_ye_daynum'] = $sku_ids_new[$vo['sku_id']];
                    $vo['fen_ye_day'] = $day;
                    $vo['fen_ye_num'] = $vo['label_id']==2?$vo['fen_ye_daynum']:ceil(($vo['fen_ye_daynum']*$day)/$vo['daizhuang']);
                    $vo['fen_ye_price'] = $vo['fen_ye_num']*$vo['price'];
                    $fen_ye_total = $fen_ye_total + $vo['fen_ye_num']*$vo['price'];
                    //判断库存
                    if ($vo['fen_ye_num'] > $vo['stock']){
                        return error('-1',$vo['sku_name'].'产品库存不足，仅剩'.$vo['stock']);
                    }
                    $fen_ye_detail[]=$vo;
                }else{
                    $vo['fen_ye_daynum'] = $sku_ids_new[$vo['sku_id']];
                    $vo['fen_ye_day'] = $day;
                    $vo['fen_ye_num'] = $sku_ids_new[$vo['sku_id']]*$day;
                    $vo['fen_ye_price'] = $vo['fen_ye_num']*$vo['price'];
                    //判断库存
                    if ($vo['fen_ye_num'] > $vo['stock']){
                        return error('-1',$vo['sku_name'].'产品库存不足，仅剩'.$vo['stock']);
                    }
                    $guding_detail[]=$vo;
                    $guding_total = $guding_total+ $sku_ids_new[$vo['sku_id']]*$day*$vo['price'];
                }
            }
            //新增购物车
            $cart_ids = '';
            foreach ($guding_detail as $key=>$vo){
                $data=['site_id'=>1,'member_id'=>$member_id,'sku_id'=>$vo['sku_id'],'num'=>$vo['fen_ye_num']];
                $res = model("goods_cart")->add($data);
                $cart_ids = $cart_ids.','.$res;
            }
            //特殊产品 不包含天数等处方要求
            if ($cart_ids){
                $cart_ids = $cart_ids.',';
            }
            foreach ($fen_ye_detail as $key=>$vo){
                $data=['site_id'=>1,'member_id'=>$member_id,'sku_id'=>$vo['sku_id'],'num'=>$vo['fen_ye_num']];
                $res = model("goods_cart")->add($data);
                $cart_ids = $cart_ids.','.$res;
            }
            foreach ($goodslist_t as $key =>$vo){
                if ($vo['stock']<1 || $vo['goods_state']==0){
                    return error('-1',$vo['sku_name'].'产品已下架');
                }
                //判断粉剂桶数
                if($vo['label_id']!=3){
                    return error('-1',$vo['sku_name'].'产品不属于出差周转包，请移除');
                }
            }
            //粉剂分装情况下才进行统计
            //自动判断是否存在粉剂
            $fenji = $fenji_t > 0 ? 1:0;
            if ($fenji==1){
                if ($c_fenji==1){
                    $fenji_t = $fenji_t;
                }else{
                    $fenji_t = 0;
                }
            }
            //每种时间产品数量，用于判断产品不能超过8粒
            $service_numdetail = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                if (in_array($ivv[1],$fen_ye)){
                    continue;
                }
                $service_numdetail[$ivv[0]]=(isset($service_numdetail[$ivv[0]])?$service_numdetail[$ivv[0]]:0)+$goods_num[$item];
            }
            //过滤单独粉剂，液体包装费
            $service_num = 0;
            $key_time_name = ['earlymoring'=>'晨起','moning'=>'早餐','aftnoon'=>'午餐','canjian'=>'餐间','night'=>'晚餐','evening'=>'睡前'];
            foreach ($service_n as $key=>$vo){
                if (count($vo)>1){
                    $service_num = $service_num+1;
                }else{
                    if (!in_array($vo[0],$fen_ye)){
                        $service_num = $service_num+1;
                    }
                }
                //过滤粉剂，液体。判断每个时间段产品不能超过8种
                if (isset($service_numdetail[$key]) && $service_numdetail[$key]>8){
                    return error('-1',(isset($key_time_name[$key])?$key_time_name[$key]:$key).'包产品不能超过8种');
                }
            }
            //判断外盒服务费
            $waihe = 1;
            if (!$goods_num && $goods_t_num){
                $waihe = 0;
            }
            $month = ceil(($day/30));

            $remarkk = '';
            if ($ccheck==1){
                $remarkk = '查验不封口;';
            }else{
                $remarkk = '不查验封口;';
            }

            if ($fenji==1){
                if ($c_fenji==1){
                    $remarkk = $remarkk. '粉剂分装';
                }else{
                    $remarkk = $remarkk. '粉剂不分装';
                }
            }

            //提交生成订单
            $order_create = new OrderCreateModel();
            $data         = [
                'cart_ids'        => $cart_ids,
                'sku_id'          => '',
                'num'             => '',
                'site_id'         => 1,//站点id
                'member_id'       => $member_id,
                'admin_uid'       => $this->uid,
                'is_balance'      => 0,//是否使用余额
                'is_point'        => 1,//是否使用积分
                'order_from'      => 'pc',
                'order_from_name' => 'PC',
                'pay_password'    => '',//支付密码
                'buyer_message'   => '', //购买人留言信息
                'delivery'        => [], //配送方式 快递 门店 外卖 默认快递
                'coupon'          => [],
                'member_address'  => [], //使用默认收货地址

                'latitude'  => null,
                'longitude' => null,

                'is_invoice'              =>  0,//发票相关
                'invoice_type'            =>  0,
                'invoice_title'           =>  '',
                'taxpayer_number'         =>  '',
                'invoice_content'         =>  '',
                'invoice_full_address'    =>  '',
                'is_tax_invoice'          =>  0,
                'invoice_email'           =>  '',
                'invoice_title_type'      =>  0,
                'buyer_ask_delivery_time' =>  '',
                'remarkk'                 => $remarkk,
            ];
            if (empty($data['cart_ids']) && empty($data['sku_id'])) {
                return error('-1', '缺少必填参数商品数据');
            }
            //增加服务费，使用配送服务费接口
            $config_model = new ConfigModel();
            $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
            $data['service_money'] =$guding_detail?$order_event_time_config['data']['value']['service_money']*$service_num*$month:0;
            $data['invoice_money'] = $guding_detail?$order_event_time_config['data']['value']['waihe_money']*$month*$waihe:0;
            $data['promotion_money'] = $guding_detail?$order_event_time_config['data']['value']['fenji_money']*$fenji_t*$month:0;
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            $data['invoice_rate'] = $user['balancediscount'];
            //判断价格
            if (($guding_total + $data['service_money'] + $data['invoice_money'] + $data['promotion_money'])>5000){
                return error('-1', '个性化产品订单金额不能超过5000');
            }
            if (($fen_ye_total)>5000){
                return error('-1', '整瓶产品订单金额不能超过5000');
            }
            $res = $order_create->create($data);
            //线下支付订单
            //$order_pay = model('order')->getInfo([['out_trade_no', "=",$res['data']]]);
            //$order_p = new OrderModel();
            //$order_p->orderPay($order_pay,'OFFLINE_PAY');
            if ($res['code'] !=0 ){
                return error('-1', $res['message']);
            }
            //增加订单处方
            $goods_name = input('goods_name',[]);
            $goods_num = input('goods_nums',[]);
            $goods_t_name = input('goods_t_name',[]);
            $goods_t_num = input('goods_t_nums',[]);
            $earlymoning_time = input('earlymoning_time', '');
            $moning_time = input('moning_time', '');
            $aftnoon_time = input('aftnoon_time', '');
            $canjian_time = input('canjian_time', '');
            $night_time = input('night_time', '');
            $sleep_time = input('sleep_time', '');
            $progress = input('progress', '');
            $day = input('day',0);
            $order_attr       = [
                'order_id' => $res['data'],
                'site_id' => $this->site_id,
                'goods_name' =>json_encode(array_merge($goods_name,$goods_t_name)),
                'goods_num'  =>json_encode(array_merge($goods_num,$goods_t_num)),
                'goods_t_name' =>json_encode($goods_t_name),
                'goods_t_num'  =>json_encode($goods_t_num),
                'day'        => $day,
                'sku_ids'    => $sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'progress'    => $progress,
                'zhongdian'   =>$zhongdian,
                'jiankangguanli'=>$jiankangguanli,
                'ccheck'    => $ccheck,
                'fenji'    => $fenji,
                'c_fenji'    => $c_fenji,
                'service_money'    => $service_money,
            ];
            $attr_id   = model("order_attr")->add($order_attr);

            //分装模板详细
            $fen_goods_name = [];
            $fen_goods_num  = [];
            $fen_sku_ids    = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                if (in_array($ivv[1],$fen_ye)){
                    continue;
                }
                $fen_sku_ids[] = $ivv[1];
                $fen_goods_name[] = $iv;
                $fen_goods_num[] = $goods_num[$item];
            }
            $fen_sku_ids = implode(',',$fen_sku_ids);
            $attr_fenzhuang       = [
                'order_id' => $res['data'],
                'site_id' => $this->site_id,
                'goods_name' =>json_encode(array_merge($fen_goods_name,$goods_t_name)),
                'goods_num'  =>json_encode(array_merge($fen_goods_num,$goods_t_num)),
                'goods_t_name' =>json_encode($goods_t_name),
                'goods_t_num'  =>json_encode($goods_t_num),
                'day'        => $day,
                'sku_ids'    => $fen_sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'progress'    => $progress,
                'zhongdian'   =>$zhongdian,
                'jiankangguanli'=>$jiankangguanli,
                'ccheck'    => $ccheck,
                'fenji'    => $fenji,
                'c_fenji'    => $c_fenji,
                'service_money'    => $service_money,
            ];
            $attr_id   = model("order_attr_fenzhuang")->add($attr_fenzhuang);

            //请求生成外部支付订单
            $order_extend = model('order')->getInfo([['out_trade_no', "=",$res['data']]]);
            $member = model('member')->getInfo([['member_id', "=", $order_extend['member_id']]]);
            $doctor = model('user')->getInfo([['uid', "=", $member['user_id']]]);
            $doctor_n = $doctor?$doctor['username']:'';
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctorList();
            $jigou = '';
            if ($user_bpdotocr[$member['user_id']]['bp']){
                $jigou = $jigou.$user_bpdotocr[$member['user_id']]['bp'];
            }
            if ($guding_detail){
                $province = model("area")->getInfo([['id', '=', $province_id]]);
                $city = model("area")->getInfo([['id', '=', $city_id]]);
                $district = model("area")->getInfo([['id', '=', $district_id]]);
                $ordersn = 'CF'.date('YmdHis'). mt_rand(1000, 9999);
                $totalprice = $guding_total + $data['service_money'] + $data['invoice_money'] + $data['promotion_money'];
                $address1 = [];
                $address1['realname']=$addressname;
                $address1['mobile']=$addressmobile;
                $address1['address']=$address;
                $address1['province']=$province['name'];
                $address1['city']=$city['name'];
                $address1['area']=$district['name'];
                //身份证号，姓名设置为空
                $address1['identitycard'] = '';
                $address1['cardrealname'] = '';
                $order    = array(
                    'uniacid' => 1,
                    'openid' => $member['wx_openid']?$member['wx_openid']:'U230504104271706376',
                    'ordersn' => $ordersn,
                    'price' => $totalprice,
                    'goodsprice'=>$guding_total,
                    'cash' => 0,
                    'discountprice' => 0,
                    'deductprice' => 0,
                    'deductcredit' => 0,
                    'deductenough' => 0,
                    'status' => 0,
                    'paytype' => 0,
                    'transid' => '',
                    'remark' => '分包订单：'.$order_extend['order_no'].' 机构：'.$jigou.' /医生：'.$doctor_n.' /客户：'.$member['username'],
                    'addressid' => 1,
                    'dispatchprice' => $data['service_money'] + $data['invoice_money'] + $data['promotion_money'],
                    'dispatchtype' => 0,
                    'dispatchid' => 0,
                    "storeid" => 0,
                    'carrier' => 0,
                    'createtime' => time(),
                    'isverify' => 0,
                    'verifycode' => '',
                    'virtual' => '',
                    'isvirtual' => 0,
                    'oldprice' => $guding_total,
                    'olddispatchprice' => $data['service_money'] + $data['invoice_money'] + $data['promotion_money'],
                    'address' => '',
                    "couponid" => $order_extend['order_no'],
                    "couponprice" => 0,
                    "cangku" => 3
                );
                $order['address'] = serialize($address1);
                $order['yushou'] = 1;
                $orderid = Db::connect('v3')->table('baijiacms_eshop_order')->insertGetId($order);
                foreach ($guding_detail as $ky=> $iv) {

                    $order_goods_detail = Db::connect('v3')->table('baijiacms_eshop_goods')->where('productsn','=',$iv['sku_no'])->find();
                    $totalprice = $iv['price']*$iv['fen_ye_num'];
                    $order_goods = array(
                        'uniacid' => 1,
                        'orderid' => $orderid,
                        'goodsid' => $order_goods_detail['id'],
                        'price' => $totalprice,
                        'total' => $iv['fen_ye_num'],
                        'optionid' => 0,
                        'createtime' => time(),
                        'optionname' => '',
                        'goodssn' => 0,
                        'productsn' => $order_goods_detail['productsn'],
                        "realprice" => $totalprice,
                        "oldprice" => $totalprice,
                        "openid" => $member['wx_openid']?$member['wx_openid']:'U230504104271706376',
                    );
                    Db::connect('v3')->table('baijiacms_eshop_order_goods')->insert($order_goods);
                    Db::connect('v3')->table('baijiacms_eshop_order_taocan')->insert($order_goods);

                }
                $res = model("order")->update(['extend_id' =>$ordersn], [['order_id', '=', $order_extend['order_id']]]);
            }

            if ($fen_ye_detail){
                $province = model("area")->getInfo([['id', '=', $province_id]]);
                $city = model("area")->getInfo([['id', '=', $city_id]]);
                $district = model("area")->getInfo([['id', '=', $district_id]]);
                $ordersn = 'CF'.date('YmdHis'). mt_rand(1000, 9999);
                $totalprice = $fen_ye_total;
                $address1 = [];
                $address1['realname']=$addressname;
                $address1['mobile']=$addressmobile;
                $address1['address']=$address;
                $address1['province']=$province['name'];
                $address1['city']=$city['name'];
                $address1['area']=$district['name'];
                //身份证号，姓名设置为空
                $address1['identitycard'] = '';
                $address1['cardrealname'] = '';
                $order    = array(
                    'uniacid' => 1,
                    'openid' => $member['wx_openid']?$member['wx_openid']:'U230504104271706376',
                    'ordersn' => $ordersn,
                    'price' => $totalprice,
                    'goodsprice'=>$totalprice,
                    'cash' => 0,
                    'discountprice' => 0,
                    'deductprice' => 0,
                    'deductcredit' => 0,
                    'deductenough' => 0,
                    'status' => 0,
                    'paytype' => 0,
                    'transid' => '',
                    'remark' => '分包订单：'.$order_extend['order_no'].' 机构：'.$jigou.' /医生：'.$doctor_n.' /客户：'.$member['username'],
                    'addressid' => 1,
                    'dispatchprice' => 0,
                    'dispatchtype' => 0,
                    'dispatchid' => 0,
                    "storeid" => 0,
                    'carrier' => 0,
                    'createtime' => time(),
                    'isverify' => 0,
                    'verifycode' => '',
                    'virtual' => '',
                    'isvirtual' => 0,
                    'oldprice' => $totalprice,
                    'olddispatchprice' => 0,
                    'address' => '',
                    "couponid" => $order_extend['order_no'],
                    "couponprice" => 0,
                    "cangku" => 0
                );
                $order['address'] = serialize($address1);
                $order['yushou'] = 1;
                $orderid = Db::connect('v3')->table('baijiacms_eshop_order')->insertGetId($order);
                foreach ($fen_ye_detail as $ky=> $iv) {
                    $order_goods_detail = Db::connect('v3')->table('baijiacms_eshop_goods')->where('productsn','=',ltrim($iv['sku_no'], 'P'))->find();
                    $totalprice = $iv['price']*$iv['fen_ye_num'];
                    $order_goods = array(
                        'uniacid' => 1,
                        'orderid' => $orderid,
                        'goodsid' => $order_goods_detail['id'],
                        'price' => $totalprice,
                        'total' => $iv['fen_ye_num'],
                        'optionid' => 0,
                        'createtime' => time(),
                        'optionname' => '',
                        'goodssn' => 0,
                        'productsn' => $order_goods_detail['productsn'],
                        "realprice" => $totalprice,
                        "oldprice" => $totalprice,
                        "openid" => $member['wx_openid']?$member['wx_openid']:'U230504104271706376',
                    );
                    Db::connect('v3')->table('baijiacms_eshop_order_goods')->insert($order_goods);
                    Db::connect('v3')->table('baijiacms_eshop_order_taocan')->insert($order_goods);
                }
                $res = model("order")->update(['invoice_full_address' =>$ordersn], [['order_id', '=', $order_extend['order_id']]]);
            }

            return success(0, 'SUCCESS',['order_id'=>$order_extend['order_id'],'ordersn'=>$ordersn]);

        }
        //获取会员列表
        $condition = [];
        //医生组获取自己的订单
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["user_id", "=", $this->uid];
                }

            }else{
                $condition[] = ["user_id", "=", $this->uid];
            }
        }
        $memberlist =  model('member')->pageList($condition, '*', 'member_id desc', 1, 0);
        $memberlist = $memberlist['list'];
        foreach($memberlist as $key=>$vo){
            if ($vo['username']!=$vo['nickname']){
                $memberlist[$key]['username'] = $vo['username'].' 昵称:'.$vo['nickname'];
            }
        }
        $this->assign("member_list", $memberlist);
        //获取处方模板列表
        $attrlist =  model('order_attr_class')->pageList(['uid'=>$this->uid,'guding'=>0], '*', 'class_id desc', 1, 0);
        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctoruidList();
        $attrgudinglist = [];
        if ($user_bpdotocr[$this->uid]['bp']){
            $attrgudinglist =  model('order_attr_class')->pageList(['uid'=>$user_bpdotocr[$this->uid]['bp'],'guding'=>1], '*', 'class_id desc', 1, 0);
            $attrgudinglist = $attrgudinglist['list'];
            foreach($attrgudinglist as $key=>$vo){
                $attrgudinglist[$key]['class_name'] = '(推荐)'.$vo['class_name'];
            }
        }
        //获取机构推荐模版
        $attrlist = $attrlist['list'];
        $this->assign("attr_list", $attrlist);
        //机构模板
        $this->assign("attr_list2", $attrgudinglist);
        //系统模板
        $attrxitonglist =  model('order_attr_class')->pageList(['uid'=>1,'guding'=>1], '*', 'class_id desc', 1, 0);
        $this->assign("attr_list3", $attrxitonglist['list']);

        //查询省级数据列表
        $address_model = new AddressModel();
        $list          = $address_model->getAreaList([["pid", "=", 0], ["level", "=", 1]]);
        $this->assign("province_list", $list["data"]);

        //获取订单常用收货地址
        $order_deverys = model('order')->getList([['admin_uid', "=", $this->uid]],"count('address') as num,address,name,mobile",'num desc','','','address,name,mobile');
        $this->assign("order_deverys", $order_deverys);
        //获取会员默认收货地址
        $config_model = new ConfigModel();
        $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
        $this->assign("service_money", $order_event_time_config['data']['value']['service_money']);

        if ($user_bpdotocr[$this->uid]['bp']){
            $user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$this->uid]['bp']]]);
        }
        if ($user['shouhuodizhi']==1){
            $member_id = $user['member_id'];
        }else{
            $member_id = 0;
        }
        $this->assign("guding_member_id", $member_id);

        $this->assign("discount_info", []);
        return $this->fetch('order/manual');
    }

    /*
     * 服务费计算
     * */
    public function fuwufei(){
        if (request()->isAjax()) {

            $day = input('day', 0);
            $fenji = input('fenji', 0);
            $c_fenji = input('c_fenji', 0);

            $goods_num = input('goods_nums',[]);
            $goods_name = input('goods_name',[]);
            $goods_t_num = input('goods_t_nums',[]);
            $goods_t_name = input('goods_t_name',[]);
            $sku_ids_a = [];
            $service_n = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                $sku_ids_a[$ivv[1]]=(isset($sku_ids_a[$ivv[1]])?$sku_ids_a[$ivv[1]]:0)+$goods_num[$item];
                //外包单
                $service_n[$ivv[0]][]=$ivv[1];
            }

            $sku_ids_new = $sku_ids_a;
            //判断商品是否还有库存，是否已下架
            $alias       = 'og';
            $join        = [
                [
                    'goods o',
                    'o.goods_id = og.goods_id',
                    'left'
                ]
            ];
            $goodslist = model('goods_sku')->getList([['og.sku_id','in',array_keys($sku_ids_new)]], 'og.*,o.goods_chi,o.tip,o.category_json,o.label_id,o.goods_attr_format as goods_attr_format_o', 'og.sku_id desc', $alias, $join);
            //重新整理产品，如果有粉剂液体方便展示
            foreach ($goodslist as $key =>$v){
                //获取分类名称
                $category_json  = json_decode($v['category_json']);
                $goods_category = '';
                foreach ($category_json as $kk => $vo) {
                    if (!empty($vo)) {
                        $category_name      = model('goods_category')->getColumn([['category_id', 'in', $vo]], 'category_name');
                        $category_name      = implode('/', $category_name);
                        $goods_category     = $category_name. '  ' .$goods_category;
                    }
                }
                $goodslist[$key]['sku_name'] = $v['sku_name'].$v['goods_chi'];
                if ($v['tip']){
                    $goodslist[$key]['sku_name'] = $goodslist[$key]['sku_name'].'<span style="color: red">('.$v['tip'].')</span>';
                }
                $goodslist[$key]['sku_name'] = $goods_category.'- '.$goodslist[$key]['sku_name'];
            }
            $fenji_t = 0;
            //粉剂，液体
            $fen_ye = [];
            $fen_ye_detail = [];
            $fen_ye_total = 0;
            $guding_total = 0;
            //商品属性成分
            $goods_attr_formats = [];
            foreach ($goodslist as $key =>$vo){
                //统计商品属性成分
                if ($vo['goods_attr_format_o']){
                    $goods_attr_format = json_decode($vo['goods_attr_format_o'],true);
                    foreach ($goods_attr_format as $kkk=>$vvv){
                        //$goods_attribute = model('goods_attribute')->getInfo([['attr_id', "=", $vvv['attr_id']]);
                        //$vvv['attr_name_max'] = isset($goods_attribute)?$goods_attribute['attr_name_max']:'';
                        $vvv['attr_value_name'] = $vvv['attr_value_name']*$sku_ids_new[$vo['sku_id']];
                        if ((strpos($vvv['attr_name'], '钒') !== false)||(strpos($vvv['attr_name'], '硼') !== false)||(strpos($vvv['attr_name'], '氯') !== false)||(strpos($vvv['attr_name'], '钠') !== false)||(strpos($vvv['attr_name'], '碘') !== false)||(strpos($vvv['attr_name'], '钼') !== false)||(strpos($vvv['attr_name'], '锰') !== false)||(strpos($vvv['attr_name'], '铬') !== false)||(strpos($vvv['attr_name'], '铜') !== false)||(strpos($vvv['attr_name'], '硒') !== false)||(strpos($vvv['attr_name'], '磷') !== false)||(strpos($vvv['attr_name'], '钾') !== false)||(strpos($vvv['attr_name'], '铁') !== false)||(strpos($vvv['attr_name'], '锌') !== false)||(strpos($vvv['attr_name'], '镁') !== false)||(strpos($vvv['attr_name'], '钙') !== false)||(strpos($vvv['attr_name'], '维生素') !== false)) {
                            if (isset($goods_attr_formats[$vvv['attr_name']])) {
                                $goods_attr_formats[$vvv['attr_name']]['attr_value_name'] = $goods_attr_formats[$vvv['attr_name']]['attr_value_name'] + $vvv['attr_value_name'];
                            } else {
                                $goods_attr_formats[$vvv['attr_name']] = $vvv;
                            }
                        }
                    }
                    ksort($goods_attr_formats);
                }
                //判断粉剂桶数+液体
                if($vo['label_id']==1||$vo['label_id']==2){
                    $fenji_t = $fenji_t + $sku_ids_new[$vo['sku_id']];
                    //判断粉剂数量 服务费计算不需要这个
                    /*if ($sku_ids_new[$vo['sku_id']]*$day<30 || $sku_ids_new[$vo['sku_id']]*$day%30!=0){
                        return error('-1',$vo['sku_name'].'产品属于粉剂，订单数量须是30的倍数');
                    }*/
                }
                //判断粉剂液体
                if($vo['label_id']==1||$vo['label_id']==2){
                    $fen_ye[] = $vo['sku_id'];
                    $vo['fen_ye_daynum'] = $sku_ids_new[$vo['sku_id']];
                    $vo['fen_ye_day'] = $day;
                    $vo['fen_ye_num'] = $vo['label_id']==2?$vo['fen_ye_daynum']:ceil(($vo['fen_ye_daynum']*$day)/$vo['daizhuang']);
                    $fen_ye_total = $fen_ye_total + $vo['fen_ye_num']*$vo['price'];
                    $fen_ye_detail[]=$vo;
                }else{
                    $guding_total = $guding_total+ $sku_ids_new[$vo['sku_id']]*$day*$vo['price'];
                }
            }
            foreach ($goods_attr_formats as $key=>$vo){
                $goods_attr_formats[$key]['attr_value_name'] = number_format($vo['attr_value_name'],2);
            }
            //粉剂分装情况下才进行统计
            //自动判断是否存在粉剂
            $fenji = $fenji_t > 0 ? 1:0;
            if ($fenji==1){
                if ($c_fenji==1){
                    $fenji_t = $fenji_t;
                }else{
                    $fenji_t = 0;
                }
            }
            //每种时间产品数量，用于判断产品不能超过8粒
            $service_numdetail = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                if (in_array($ivv[1],$fen_ye)){
                    continue;
                }
                $service_numdetail[$ivv[0]]=(isset($service_numdetail[$ivv[0]])?$service_numdetail[$ivv[0]]:0)+$goods_num[$item];
            }
            //过滤单独粉剂，液体包装费
            $service_num = 0;
            $key_time_name = ['earlymoring'=>'晨起','moning'=>'早餐','aftnoon'=>'午餐','canjian'=>'餐间','night'=>'晚餐','evening'=>'睡前'];
            foreach ($service_n as $key=>$vo){
                if (count($vo)>1){
                    $service_num = $service_num+1;
                }else{
                    if (!in_array($vo[0],$fen_ye)){
                        $service_num = $service_num+1;
                    }
                }
                //过滤粉剂，液体。判断每个时间段产品不能超过8种
                if (isset($service_numdetail[$key]) && $service_numdetail[$key]>8){
                    return error('-1',(isset($key_time_name[$key])?$key_time_name[$key]:$key).'包产品不能超过8种');
                }
            }
            //判断外盒服务费
            $waihe = 1;
            if (!$goods_num && $goods_t_num){
                $waihe = 0;
            }
            $month = ceil(($day/30));
            //增加服务费，使用配送服务费接口
            $config_model = new ConfigModel();
            $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
            $data['service_money'] = $guding_total>0?$order_event_time_config['data']['value']['service_money']*$service_num*$month:0;
            $data['invoice_money'] = $guding_total>0?$order_event_time_config['data']['value']['waihe_money']*$month*$waihe:0;
            $data['promotion_money'] = $guding_total>0?$order_event_time_config['data']['value']['fenji_money']*$fenji_t*$month:0;
            $res = [
                'fenji'=>$fenji,
                'earlymoring_time'=>isset($service_numdetail['earlymoring'])?1:0,
                'moning_time'=>isset($service_numdetail['moning'])?1:0,
                'aftnoon_time'=>isset($service_numdetail['aftnoon'])?1:0,
                'canjian_time'=>isset($service_numdetail['canjian'])?1:0,
                'night_time'=>isset($service_numdetail['night'])?1:0,
                'evening_time'=>isset($service_numdetail['evening'])?1:0,
                'fen_ye_detail'=>$fen_ye_detail,
                'fen_ye_total'=>$fen_ye_total,
                'guding_total'=>$guding_total,
                'goods_attr_formats'=>$goods_attr_formats,
                'money'=>$fen_ye_total+$guding_total+$data['service_money']+$data['invoice_money']+$data['promotion_money'],
                'remark'=>$guding_total.'(分装产品费用)+'.$service_num.'*'.$order_event_time_config['data']['value']['service_money'].'*'.$month.'(单独餐盒费)+'.$order_event_time_config['data']['value']['waihe_money'].'*'.$month.'*'.$waihe.'(订单外盒费)+'.$fenji_t.'*'.$order_event_time_config['data']['value']['fenji_money'].'*'.$month.'(粉剂分装费)='.($data['service_money']+$data['invoice_money']+$data['promotion_money']+$guding_total),
                'fen_ye_remark'=>$fen_ye_total.'(整瓶产品费用)'
            ];
            return success(0, 'SUCCESS',$res);

        }
    }

    /**
     * 生成订单
     */
    public function manuak_w(){
        if (request()->isAjax()) {
            //判断下单用户
            /*$member_id = input('member_id', 0);
            if (!$member_id){
                $user = model('user')->getInfo([['uid', "=", $this->uid]]);
                $member_id = $user['member_id'];
            }*/
            //外部订单传入失败不返回错误，直接记录
            $error = 0;
            $error_msg = '';
            $member_name = input('member_name', '');
            if (!$member_name){
                $error = 1;
                $error_msg = $error_msg.' '.'请传入会员信息';
                //return error(-1,'请传入会员信息');
            }
            $member = model('member')->getInfo([['username', "=", $member_name],['user_id', "=", 316]]);
            if ($member){
                $member_id = $member['member_id'];
            }else{
                $data = [
                    'site_id'           => $this->site_id,
                    'user_id'           => 316,
                    'username'          => $member_name,
                    'mobile'            => input('mobile', ''),
                    'email'             => input('email', ''),
                    'password'          => data_md5('123456'),
                    'status'            => input('status', 1),
                    'headimg'           => input('headimg', ''),
                    'member_level'      => input('member_level', ''),
                    'member_level_name' => input('member_level_name', ''),
                    'nickname'          => $member_name,
                    'sex'               => input('sex', 0),
                    'birthday'          => input('birthday', '') ? strtotime(input('birthday', '')) : 0,
                    'realname'          => input('realname', ''),
                    'reg_time'          => time(),
                    'goods_image'       => input("goods_image", ""),// 商品主图路径
                    'goods_content'     => input("goods_content", ""),// 商品详情
                ];
                $res = model('member')->add($data);
                $member_id = $res;

            }
            //生成后台用户收货地址
            $addressname = input('member_name', '');
            $addressmobile = input('mobile', '');
            $address = input('address', '');
            $province_id = input('province_id', '');
            $city_id = input('city_id', '');
            $district_id = input('district_id', '');
            $day = input('day', 0);
            $fenji = input('fenji', 0);
            $ccheck = input('ccheck', 0);
            $c_fenji = input('c_fenji', 0);
            $service_money = input('service_money', '');
            $diyprice = input('diyprice', '');

            if (!$ccheck){
                $error = 1;
                $error_msg = $error_msg.' '.'请选择是否二次查验';
                //return error('-1','请选择是否二次查验');
            }

            if (!$c_fenji && $fenji==1){
                $error = 1;
                $error_msg = $error_msg.' '.'请选择粉剂是否分装';
                //return error('-1','请选择粉剂是否分装');
            }

            if ($day<1){
                $error = 1;
                $error_msg = $error_msg.' '.'疗程天数不能小于1天';
                //return error('-1','疗程天数不能小于1天');
            }
            if (!is_numeric($day)||strpos($day,".")!==false){
                $error = 1;
                $error_msg = $error_msg.' '.'疗程天数必须是整数';
                //return error('-1','疗程天数必须是整数');
            }
            if ($addressname&&$addressmobile&&$address&&$province_id&&$city_id&&$district_id){
            }else{
                $error = 1;
                $error_msg = $error_msg.' '.'请填写完整收货地址';
                //return error('-1','请填写完整收货地址');
            }
            //健康营养师以及干预重点
            $jiankangguanli = input('jiankangguanli', ' ');
            $zhongdian = input('zhongdian', ' ');
            /*if ($jiankangguanli){
            }else{
                return error('-1','请填写健康营养师名称');
            }
            if ($zhongdian){
            }else{
                return error('-1','请填写本期营养素干预重点');
            }*/
            $member_address = model("member_address")->getInfo([['member_id', '=', $member_id], ['is_default', '=', 1]], 'id');
            if (!empty($member_address)) {
                $res = model("member_address")->update(['name' =>$addressname,'mobile'=>$addressmobile,'address'=>$address,'telephone'=>$addressmobile,'province_id'=>$province_id,'city_id'=>$city_id,'district_id'=>$district_id], [['id', '=', $member_address['id']]]);
            } else {
                $res = model("member_address")->add(['member_id'=>$member_id,'site_id'=>1,'is_default'=>1,'name' =>$addressname,'mobile'=>$addressmobile,'address'=>$address,'telephone'=>$addressmobile,'province_id'=>$province_id,'city_id'=>$city_id,'district_id'=>$district_id]);
            }

            //生成购物车,重新整理处方商品信息
            model("goods_cart")->delete([['member_id', '=', $member_id]]);
            $sku_ids = input('sku_ids', '');

            $goods_num = input('goods_nums',[]);
            $goods_name = input('goods_name',[]);
            //特殊产品
            $goods_t_num = input('goods_t_nums',[]);
            $goods_t_name = input('goods_t_name',[]);
            $sku_ids_t_a = [];
            $sku_ids_a = [];
            $service_n = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                $sku_ids_a[$ivv[1]]=(isset($sku_ids_a[$ivv[1]])?$sku_ids_a[$ivv[1]]:0)+$goods_num[$item];
                //外包单
                $service_n[$ivv[0]][]=$ivv[1];
            }
            //特殊产品
            foreach ($goods_t_name as $item=>$iv){
                $ivv = explode('_',$iv);
                $sku_ids_t_a[$ivv[1]]=(isset($sku_ids_t_a[$ivv[1]])?$sku_ids_t_a[$ivv[1]]:0)+$goods_t_num[$item];
            }
            if (!$goods_num && !$goods_t_num){
                $error = 1;
                $error_msg = $error_msg.' '.'请选择处方商品';
                //return error('-1','请选择处方商品');
            }
            $sku_ids_new = $sku_ids_a;
            /*foreach ($sku_ids_a as $key =>$vo){
                foreach ($sku_ids_a as $k =>$v){
                    if ($vo == $v){
                        $sku_ids_new[$v] = (isset($sku_ids_new[$v])?$sku_ids_new[$v]:0)+$goods_num[$k];
                        $goods_num[$k] = 0;
                    }
                }
            }*/
            //新增购物车
            $cart_ids = '';
            foreach ($sku_ids_new as $key=>$vo){
                $data=['site_id'=>1,'member_id'=>$member_id,'sku_id'=>$key,'num'=>$vo*$day];
                $res = model("goods_cart")->add($data);
                $cart_ids = $cart_ids.','.$res;
            }
            //特殊产品 不包含天数等处方要求
            if ($cart_ids){
                $cart_ids = $cart_ids.',';
            }
            foreach ($sku_ids_t_a as $key=>$vo){
                $data=['site_id'=>1,'member_id'=>$member_id,'sku_id'=>$key,'num'=>$vo];
                $res = model("goods_cart")->add($data);
                $cart_ids = $cart_ids.','.$res;
            }
            //判断商品是否还有库存，是否已下架
            $alias       = 'og';
            $join        = [
                [
                    'goods o',
                    'o.goods_id = og.goods_id',
                    'left'
                ]
            ];
            $goodslist = model('goods_sku')->getList([['og.sku_id','in',array_keys($sku_ids_new)]], 'og.*,o.label_id', 'og.sku_id desc', $alias, $join);
            $goodslist_t = model('goods_sku')->getList([['og.sku_id','in',array_keys($sku_ids_t_a)]], 'og.*,o.label_id', 'og.sku_id desc', $alias, $join);

            $fenji_t = 0;
            //粉剂，液体
            $fen_ye = [];
            foreach ($goodslist as $key =>$vo){
                if ($vo['stock']<1 || $vo['goods_state']==0){
                    $error = 1;
                    $error_msg = $error_msg.' '.$vo['sku_name'].'产品已下架';
                    //return error('-1',$vo['sku_name'].'产品已下架');
                }
                //判断数量
                if ($vo['stock']<$sku_ids_new[$vo['sku_id']]*$day){
                    $error = 1;
                    $error_msg = $error_msg.' '.$vo['sku_name'].'产品库存不足';
                }
                //判断粉剂桶数
                if($vo['label_id']==1){
                    $fenji_t = $fenji_t + $sku_ids_new[$vo['sku_id']];
                    //判断粉剂数量
                    if ($sku_ids_new[$vo['sku_id']]*$day<30 || $sku_ids_new[$vo['sku_id']]*$day%30!=0){
                        $error = 1;
                        $error_msg = $error_msg.' '.$vo['sku_name'].'产品属于粉剂，订单数量须是30的倍数';
                        //return error('-1',$vo['sku_name'].'产品属于粉剂，订单数量须是30的倍数');
                    }
                    //判断分装数量是否合适
                    if ($c_fenji==2&&$sku_ids_new[$vo['sku_id']]*$day%$vo['daizhuang']!=0){
                        $error = 1;
                        $error_msg = $error_msg.' '.$vo['sku_name'].'一桶有'.$vo['daizhuang'].'袋，处方不分装的情况每天的用量须是'.$vo['daizhuang'].'的倍数';
                        //return error('-1',$vo['sku_name'].'一桶有'.$vo['daizhuang'].'袋，处方不分装的情况每天的用量须是'.$vo['daizhuang'].'的倍数');
                    }
                }
                if($vo['label_id']==2){
                    //判断粉剂数量
                    if ($sku_ids_new[$vo['sku_id']]*$day<30 || $sku_ids_new[$vo['sku_id']]*$day%30!=0){
                        return error('-1',$vo['sku_name'].'产品属于粉剂，订单数量须是30的倍数');
                    }
                }
                //判断粉剂液体
                if($vo['label_id']==1||$vo['label_id']==2){
                    $fen_ye[] = $vo['sku_id'];
                }
            }
            foreach ($goodslist_t as $key =>$vo){
                if ($vo['stock']<1 || $vo['goods_state']==0){
                    $error = 1;
                    $error_msg = $error_msg.' '.$vo['sku_name'].'产品已下架';
                    //return error('-1',$vo['sku_name'].'产品已下架');
                }
                //判断数量
                if ($vo['stock']<$sku_ids_t_a[$vo['sku_id']]*$day){
                    $error = 1;
                    $error_msg = $error_msg.' '.$vo['sku_name'].'产品库存不足';
                }
                //判断粉剂桶数
                if($vo['label_id']!=3){
                    $error = 1;
                    $error_msg = $error_msg.' '.$vo['sku_name'].'产品不属于出差周转包，请移除';
                    //return error('-1',$vo['sku_name'].'产品不属于出差周转包，请移除');
                }
            }
            //粉剂分装情况下才进行统计
            //自动判断是否存在粉剂
            $fenji = $fenji_t > 0 ? 1:0;
            if ($fenji==1){
                if ($c_fenji==1){
                    $fenji_t = $fenji_t;
                }else{
                    $fenji_t = 0;
                }
            }
            //每种时间产品数量，用于判断产品不能超过8粒
            $service_numdetail = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                if (in_array($ivv[1],$fen_ye)){
                    continue;
                }
                $service_numdetail[$ivv[0]]=(isset($service_numdetail[$ivv[0]])?$service_numdetail[$ivv[0]]:0)+$goods_num[$item];
            }
            //过滤单独粉剂，液体包装费
            $service_num = 0;
            $key_time_name = ['earlymoring'=>'晨起','moning'=>'早餐','aftnoon'=>'午餐','canjian'=>'餐间','night'=>'晚餐','evening'=>'睡前'];
            foreach ($service_n as $key=>$vo){
                if (count($vo)>1){
                    $service_num = $service_num+1;
                }else{
                    if (!in_array($vo[0],$fen_ye)){
                        $service_num = $service_num+1;
                    }
                }
                //过滤粉剂，液体。判断每个时间段产品不能超过8种
                if (isset($service_numdetail[$key]) && $service_numdetail[$key]>8){
                    return error('-1',(isset($key_time_name[$key])?$key_time_name[$key]:$key).'包产品不能超过8种');
                }
            }
            //判断外盒服务费
            $waihe = 1;
            if (!$goods_num && $goods_t_num){
                $waihe = 0;
            }
            $month = ceil(($day/30));

            $remarkk = '';
            if ($ccheck==1){
                $remarkk = '查验不封口;';
            }else{
                $remarkk = '不查验封口;';
            }

            if ($fenji==1){
                if ($c_fenji==1){
                    $remarkk = $remarkk. '粉剂分装';
                }else{
                    $remarkk = $remarkk. '粉剂不分装';
                }
            }
            //提交生成订单
            $order_create = new OrderCreateModel();
            $data         = [
                'cart_ids'        => $cart_ids,
                'sku_id'          => '',
                'num'             => '',
                'site_id'         => 1,//站点id
                'member_id'       => $member_id,
                'admin_uid'       => $this->uid,
                'is_balance'      => 0,//是否使用余额
                'is_point'        => 1,//是否使用积分
                'order_from'      => 'pc',
                'order_from_name' => 'PC',
                'pay_password'    => '',//支付密码
                'buyer_message'   => '', //购买人留言信息
                'delivery'        => [], //配送方式 快递 门店 外卖 默认快递
                'coupon'          => [],
                'member_address'  => [
                    'name' =>$addressname,'mobile'=>$addressmobile,'address'=>$address,'telephone'=>$addressmobile,'province_id'=>$province_id,'city_id'=>$city_id,'district_id'=>$district_id
                ], //使用默认收货地址

                'latitude'  => null,
                'longitude' => null,

                'is_invoice'              =>  0,//发票相关
                'invoice_type'            =>  0,
                'invoice_title'           =>  '',
                'taxpayer_number'         =>  '',
                'invoice_content'         =>  '',
                'invoice_full_address'    =>  '',
                'is_tax_invoice'          =>  0,
                'invoice_email'           =>  '',
                'invoice_title_type'      =>  0,
                'buyer_ask_delivery_time' =>  '',
                'remarkk'                 => $remarkk,
                'remark'                  => ($error==1?$error_msg:''),
            ];
            if (empty($data['cart_ids']) && empty($data['sku_id'])) {
                return error('-1', '缺少必填参数商品数据');
            }
            //增加服务费，使用配送服务费接口
            $config_model = new ConfigModel();
            $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
            $data['service_money'] = $order_event_time_config['data']['value']['service_money']*$service_num*$month;
            $data['invoice_money'] = $order_event_time_config['data']['value']['waihe_money']*$month*$waihe;
            $data['promotion_money'] = $order_event_time_config['data']['value']['fenji_money']*$fenji_t*$month;
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            $data['invoice_rate'] = $user['balancediscount'];
            $res = $order_create->create($data,$error);
            if ($res['code'] !=0 ){
                return error('-1', $res['message']);
            }
            //增加订单处方
            $goods_name = input('goods_name',[]);
            $goods_num = input('goods_nums',[]);
            $goods_t_name = input('goods_t_name',[]);
            $goods_t_num = input('goods_t_nums',[]);
            $earlymoning_time = input('earlymoning_time', '');
            $moning_time = input('moning_time', '');
            $aftnoon_time = input('aftnoon_time', '');
            $canjian_time = input('canjian_time', '');
            $night_time = input('night_time', '');
            $sleep_time = input('sleep_time', '');
            $progress = input('progress', '');
            $day = input('day',0);
            $data       = [
                'order_id' => $res['data'],
                'site_id' => $this->site_id,
                'goods_name' =>json_encode(array_merge($goods_name,$goods_t_name)),
                'goods_num'  =>json_encode(array_merge($goods_num,$goods_t_num)),
                'goods_t_name' =>json_encode($goods_t_name),
                'goods_t_num'  =>json_encode($goods_t_num),
                'day'        => $day,
                'sku_ids'    => $sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'progress'    => $progress,
                'zhongdian'   =>$zhongdian,
                'jiankangguanli'=>$jiankangguanli,
                'ccheck'    => $ccheck,
                'fenji'    => $fenji,
                'c_fenji'    => $c_fenji,
                'service_money'    => $service_money,
            ];
            $attr_id   = model("order_attr")->add($data);

            //请求生成外部支付订单
            $order_extend = model('order')->getInfo([['out_trade_no', "=",$res['data']]]);
            $member = model('member')->getInfo([['member_id', "=", $order_extend['member_id']]]);
            $doctor = model('user')->getInfo([['uid', "=", $member['user_id']]]);
            $doctor_n = $doctor?$doctor['username']:'';
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctorList();
            $jigou = '';
            if ($user_bpdotocr[$member['user_id']]['bp']){
                $jigou = $jigou.$user_bpdotocr[$member['user_id']]['bp'];
            }
            $post_url = 'http://m.metawordtech.com/api/order_extend.php';
            $post_data = ['order_id'=>$order_extend['order_id'],'order_no'=>$order_extend['order_no'],'price'=>$diyprice,'jigou'=>$jigou,'yisheng'=>$doctor_n,'kehu'=>$member['username']];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $post_url);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

            if ($post_data != '' && !empty($post_data)) {
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
            }
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            $result = curl_exec($ch);
            curl_close($ch);
            $result = json_decode($result,true);
            if ($result['status'] == 1){
                $res = model("order")->update(['extend_id' =>$result['ordersn']], [['order_id', '=', $order_extend['order_id']]]);
            }
            //  $res['order_id'] = $order_extend['order_id'];
            return success(0, 'SUCCESS',['order_id'=>$order_extend['order_id'],'ordersn'=>$result['ordersn']]);

        }
        //获取会员列表
        $condition = [];
        //医生组获取自己的订单
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["user_id", "=", $this->uid];
                }

            }else{
                $condition[] = ["user_id", "=", $this->uid];
            }
        }
        $memberlist =  model('member')->pageList($condition, '*', 'member_id desc', 1, 0);
        $memberlist = $memberlist['list'];
        foreach($memberlist as $key=>$vo){
            if ($vo['username']!=$vo['nickname']){
                $memberlist[$key]['username'] = $vo['username'].' 昵称:'.$vo['nickname'];
            }
        }
        $this->assign("member_list", $memberlist);
        //获取处方模板列表
        $attrlist =  model('order_attr_class')->pageList(['uid'=>$this->uid,'guding'=>0], '*', 'class_id desc', 1, 0);
        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctoruidList();
        $attrgudinglist = [];
        if ($user_bpdotocr[$this->uid]['bp']){
            $attrgudinglist =  model('order_attr_class')->pageList(['uid'=>$user_bpdotocr[$this->uid]['bp'],'guding'=>1], '*', 'class_id desc', 1, 0);
            $attrgudinglist = $attrgudinglist['list'];
            foreach($attrgudinglist as $key=>$vo){
                $attrgudinglist[$key]['class_name'] = '(推荐)'.$vo['class_name'];
            }
        }
        //获取机构推荐模版
        $attrlist = $attrlist['list'];
        $attrlist = array_merge($attrlist,$attrgudinglist);
        $this->assign("attr_list", $attrlist);
        //查询省级数据列表
        $address_model = new AddressModel();
        $list          = $address_model->getAreaList([["pid", "=", 0], ["level", "=", 1]]);
        $this->assign("province_list", $list["data"]);

        //获取会员默认收货地址
        $config_model = new ConfigModel();
        $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
        $this->assign("service_money", $order_event_time_config['data']['value']['service_money']);

        //默认会员
        $member_name = input('member_name', '');
        $this->assign("member_name", $member_name);

        $this->assign("discount_info", []);
        return $this->fetch('order/manuak_w');
    }

    /**
     * 生成订单
     */
    public function manuak(){
        if (request()->isAjax()) {
            //判断下单用户
            $member_id = input('member_id', 0);
            if (!$member_id){
                $user = model('user')->getInfo([['uid', "=", $this->uid]]);
                $member_id = $user['member_id'];
            }
            //生成后台用户收货地址
            $addressname = input('addressname', '');
            $addressmobile = input('addressmobile', '');
            $address = input('address', '');
            $province_id = input('province_id', '');
            $city_id = input('city_id', '');
            $district_id = input('district_id', '');
            $day = input('day', 0);
            $fenji = input('fenji', 0);
            $ccheck = input('ccheck', 0);
            $c_fenji = input('c_fenji', 0);
            $service_money = input('service_money', '');

            //编辑原订单edit_order_id
            $edit_order_id = input('edit_order_id',0);

            if (!$ccheck){
                return error('-1','请选择是否二次查验');
            }

            if (!$c_fenji && $fenji==1){
                return error('-1','请选择粉剂是否分装');
            }

            if ($day<1){
                return error('-1','疗程天数不能小于1天');
            }
            if (!is_numeric($day)||strpos($day,".")!==false){
                return error('-1','疗程天数必须是整数');
            }
            if ($addressname&&$addressmobile&&$address&&$province_id&&$city_id&&$district_id){
            }else{
                return error('-1','请填写完整收货地址');
            }
            //健康营养师以及干预重点
            $jiankangguanli = input('jiankangguanli', ' ');
            $zhongdian = input('zhongdian', ' ');
            /*if ($jiankangguanli){
            }else{
                return error('-1','请填写健康营养师名称');
            }
            if ($zhongdian){
            }else{
                return error('-1','请填写本期营养素干预重点');
            }*/
            $member_address = model("member_address")->getInfo([['member_id', '=', $member_id], ['is_default', '=', 1]], 'id');
            if (!empty($member_address)) {
                $res = model("member_address")->update(['name' =>$addressname,'mobile'=>$addressmobile,'address'=>$address,'telephone'=>$addressmobile,'province_id'=>$province_id,'city_id'=>$city_id,'district_id'=>$district_id], [['id', '=', $member_address['id']]]);
            } else {
                $res = model("member_address")->add(['member_id'=>$member_id,'site_id'=>1,'is_default'=>1,'name' =>$addressname,'mobile'=>$addressmobile,'address'=>$address,'telephone'=>$addressmobile,'province_id'=>$province_id,'city_id'=>$city_id,'district_id'=>$district_id]);
            }

            //生成购物车,重新整理处方商品信息
            model("goods_cart")->delete([['member_id', '=', $member_id]]);
            $sku_ids = input('sku_ids', '');

            $goods_num = input('goods_nums',[]);
            $goods_name = input('goods_name',[]);
            //特殊产品
            $goods_t_num = input('goods_t_nums',[]);
            $goods_t_name = input('goods_t_name',[]);
            $sku_ids_t_a = [];
            $sku_ids_a = [];
            $service_n = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                $sku_ids_a[$ivv[1]]=(isset($sku_ids_a[$ivv[1]])?$sku_ids_a[$ivv[1]]:0)+$goods_num[$item];
                //外包单
                $service_n[$ivv[0]][]=$ivv[1];
            }
            //特殊产品
            foreach ($goods_t_name as $item=>$iv){
                $ivv = explode('_',$iv);
                $sku_ids_t_a[$ivv[1]]=(isset($sku_ids_t_a[$ivv[1]])?$sku_ids_t_a[$ivv[1]]:0)+$goods_t_num[$item];
            }
            if (!$goods_num && !$goods_t_num){
                return error('-1','请选择处方商品');
            }
            $sku_ids_new = $sku_ids_a;
            /*foreach ($sku_ids_a as $key =>$vo){
                foreach ($sku_ids_a as $k =>$v){
                    if ($vo == $v){
                        $sku_ids_new[$v] = (isset($sku_ids_new[$v])?$sku_ids_new[$v]:0)+$goods_num[$k];
                        $goods_num[$k] = 0;
                    }
                }
            }*/
            //判断商品是否还有库存，是否已下架
            $alias       = 'og';
            $join        = [
                [
                    'goods o',
                    'o.goods_id = og.goods_id',
                    'left'
                ]
            ];
            $goodslist = model('goods_sku')->getList([['og.sku_id','in',array_keys($sku_ids_new)]], 'og.*,o.label_id', 'og.sku_id desc', $alias, $join);
            $goodslist_t = model('goods_sku')->getList([['og.sku_id','in',array_keys($sku_ids_t_a)]], 'og.*,o.label_id', 'og.sku_id desc', $alias, $join);
            $goodslist_detail = [];

            $fenji_t = 0;
            //粉剂，液体
            $fen_ye = [];
            $fen_ye_detail = [];
            $fen_ye_total = 0;
            $guding_detail = [];
            $guding_total = 0;
            foreach ($goodslist as $key =>$vo){
                $goodslist_detail[$vo['sku_id']] = $vo;
                if ($vo['stock']<1 || $vo['goods_state']==0){
                    return error('-1',$vo['sku_name'].'产品已下架');
                }
                //判断粉剂桶数
                if($vo['label_id']==1){
                    $fenji_t = $fenji_t + $sku_ids_new[$vo['sku_id']];
                    //判断粉剂数量
                    if ($sku_ids_new[$vo['sku_id']]*$day<30 || $sku_ids_new[$vo['sku_id']]*$day%30!=0){
                    //    return error('-1',$vo['sku_name'].'产品属于粉剂，订单数量须是30的倍数');
                    }
                    //判断分装数量是否合适
                    if ($c_fenji==2&&$sku_ids_new[$vo['sku_id']]*$day%$vo['daizhuang']!=0){
                    //    return error('-1',$vo['sku_name'].'一桶有'.$vo['daizhuang'].'袋，处方不分装的情况每天的用量须是'.$vo['daizhuang'].'的倍数');
                    }
                }
                if($vo['label_id']==2){
                    //判断粉剂数量
                    if ($sku_ids_new[$vo['sku_id']]*$day<30 || $sku_ids_new[$vo['sku_id']]*$day%30!=0){
                    //    return error('-1',$vo['sku_name'].'产品属于粉剂，订单数量须是30的倍数');
                    }
                }
                //判断粉剂液体
                if($vo['label_id']==1||$vo['label_id']==2){
                    $fen_ye[] = $vo['sku_id'];
                    $vo['fen_ye_daynum'] = $sku_ids_new[$vo['sku_id']];
                    $vo['fen_ye_day'] = $day;
                    $vo['fen_ye_num'] = $vo['label_id']==2?$vo['fen_ye_daynum']:ceil(($vo['fen_ye_daynum']*$day)/$vo['daizhuang']);
                    $vo['fen_ye_price'] = $vo['fen_ye_num']*$vo['price'];
                    $fen_ye_total = $fen_ye_total + $vo['fen_ye_num']*$vo['price'];
                    //判断库存
                    if ($vo['fen_ye_num'] > $vo['stock']){
                        return error('-1',$vo['sku_name'].'产品库存不足，仅剩'.$vo['stock']);
                    }
                    $fen_ye_detail[]=$vo;
                }else{
                    $vo['fen_ye_daynum'] = $sku_ids_new[$vo['sku_id']];
                    $vo['fen_ye_day'] = $day;
                    $vo['fen_ye_num'] = $sku_ids_new[$vo['sku_id']]*$day;
                    $vo['fen_ye_price'] = $vo['fen_ye_num']*$vo['price'];
                    $guding_detail[]=$vo;
                    //判断库存
                    if ($vo['fen_ye_num'] > $vo['stock']){
                        return error('-1',$vo['sku_name'].'产品库存不足，仅剩'.$vo['stock']);
                    }
                    $guding_total = $guding_total+ $sku_ids_new[$vo['sku_id']]*$day*$vo['price'];
                }
            }
            //新增购物车
            $cart_ids = '';
            foreach ($guding_detail as $key=>$vo){
                $data=['site_id'=>1,'member_id'=>$member_id,'sku_id'=>$vo['sku_id'],'num'=>$vo['fen_ye_num']];
                $res = model("goods_cart")->add($data);
                $cart_ids = $cart_ids.','.$res;
            }
            //特殊产品 不包含天数等处方要求
            if ($cart_ids){
                $cart_ids = $cart_ids.',';
            }
            foreach ($fen_ye_detail as $key=>$vo){
                $data=['site_id'=>1,'member_id'=>$member_id,'sku_id'=>$vo['sku_id'],'num'=>$vo['fen_ye_num']];
                $res = model("goods_cart")->add($data);
                $cart_ids = $cart_ids.','.$res;
            }
            foreach ($goodslist_t as $key =>$vo){
                if ($vo['stock']<1 || $vo['goods_state']==0){
                    return error('-1',$vo['sku_name'].'产品已下架');
                }
                //判断库存
                if (isset($sku_ids_t_a[$vo['sku_id']]) && ($sku_ids_t_a[$vo['sku_id']] > $vo['stock'])){
                    return error('-1',$vo['sku_name'].'产品库存不足，仅剩'.$vo['stock']);
                }
                //判断粉剂桶数
                if($vo['label_id']!=3){
                    return error('-1',$vo['sku_name'].'产品不属于出差周转包，请移除');
                }
            }
            //粉剂分装情况下才进行统计
            //自动判断是否存在粉剂
            $fenji = $fenji_t > 0 ? 1:0;
            if ($fenji==1){
                if ($c_fenji==1){
                    $fenji_t = $fenji_t;
                }else{
                    $fenji_t = 0;
                }
            }
            //每种时间产品数量，用于判断产品不能超过8粒
            $service_numdetail = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                if (in_array($ivv[1],$fen_ye)){
                    continue;
                }
                $service_numdetail[$ivv[0]]=(isset($service_numdetail[$ivv[0]])?$service_numdetail[$ivv[0]]:0)+$goods_num[$item];
            }
            //过滤单独粉剂，液体包装费
            $service_num = 0;
            $key_time_name = ['earlymoring'=>'晨起','moning'=>'早餐','aftnoon'=>'午餐','canjian'=>'餐间','night'=>'晚餐','evening'=>'睡前'];
            foreach ($service_n as $key=>$vo){
                if (count($vo)>1){
                    $service_num = $service_num+1;
                }else{
                    if (!in_array($vo[0],$fen_ye)){
                        $service_num = $service_num+1;
                    }
                }
                //过滤粉剂，液体。判断每个时间段产品不能超过8种
                if (isset($service_numdetail[$key]) && $service_numdetail[$key]>8){
                    return error('-1',(isset($key_time_name[$key])?$key_time_name[$key]:$key).'包产品不能超过8种');
                }
            }
            //判断外盒服务费
            $waihe = 1;
            if (!$goods_num && $goods_t_num){
                $waihe = 0;
            }
            $month = ceil(($day/30));

            $remarkk = '';
            if ($ccheck==1){
                $remarkk = '查验不封口;';
            }else{
                $remarkk = '不查验封口;';
            }

            if ($fenji==1){
                if ($c_fenji==1){
                    $remarkk = $remarkk. '粉剂分装';
                }else{
                    $remarkk = $remarkk. '粉剂不分装';
                }
            }
            //提交生成订单
            $order_create = new OrderCreateModel();
            $data         = [
                'cart_ids'        => $cart_ids,
                'sku_id'          => '',
                'num'             => '',
                'site_id'         => 1,//站点id
                'member_id'       => $member_id,
                'admin_uid'       => $this->uid,
                'is_balance'      => 0,//是否使用余额
                'is_point'        => 1,//是否使用积分
                'order_from'      => 'pc',
                'order_from_name' => 'PC',
                'pay_password'    => '',//支付密码
                'buyer_message'   => '', //购买人留言信息
                'delivery'        => [], //配送方式 快递 门店 外卖 默认快递
                'coupon'          => [],
                'member_address'  => [
                    'name' =>$addressname,'mobile'=>$addressmobile,'address'=>$address,'telephone'=>$addressmobile,'province_id'=>$province_id,'city_id'=>$city_id,'district_id'=>$district_id
                ], //使用默认收货地址

                'latitude'  => null,
                'longitude' => null,

                'is_invoice'              =>  0,//发票相关
                'invoice_type'            =>  0,
                'invoice_title'           =>  '',
                'taxpayer_number'         =>  '',
                'invoice_content'         =>  '',
                'invoice_full_address'    =>  '',
                'is_tax_invoice'          =>  0,
                'invoice_email'           =>  '',
                'invoice_title_type'      =>  0,
                'buyer_ask_delivery_time' =>  '',
                'remarkk'                 => $remarkk,
            ];
            if (empty($data['cart_ids']) && empty($data['sku_id'])) {
                return error('-1', '缺少必填参数商品数据');
            }
            //增加服务费，使用配送服务费接口
            $config_model = new ConfigModel();
            $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
            $data['service_money'] = $guding_detail?$order_event_time_config['data']['value']['service_money']*$service_num*$month:0;
            $data['invoice_money'] = $guding_detail?$order_event_time_config['data']['value']['waihe_money']*$month*$waihe:0;
            $data['promotion_money'] = $guding_detail?$order_event_time_config['data']['value']['fenji_money']*$fenji_t*$month:0;
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            $data['invoice_rate'] = $user['balancediscount'];
            //判断价格
            if (($guding_total + $data['service_money'] + $data['invoice_money'] + $data['promotion_money'])>5000){
                return error('-1', '个性化产品订单金额不能超过5000');
            }
            if (($fen_ye_total)>5000){
                return error('-1', '整瓶产品订单金额不能超过5000');
            }
            $res = $order_create->create($data);
            if ($res['code'] !=0 ){
                return error('-1', $res['message']);
            }
            //增加订单处方
            $goods_name = input('goods_name',[]);
            $goods_num = input('goods_nums',[]);
            $goods_t_name = input('goods_t_name',[]);
            $goods_t_num = input('goods_t_nums',[]);
            $earlymoning_time = input('earlymoning_time', '');
            $moning_time = input('moning_time', '');
            $aftnoon_time = input('aftnoon_time', '');
            $canjian_time = input('canjian_time', '');
            $night_time = input('night_time', '');
            $sleep_time = input('sleep_time', '');
            $progress = input('progress', '');
            $day = input('day',0);
            $order_attr      = [
                'order_id' => $res['data'],
                'site_id' => $this->site_id,
                'goods_name' =>json_encode(array_merge($goods_name,$goods_t_name)),
                'goods_num'  =>json_encode(array_merge($goods_num,$goods_t_num)),
                'goods_t_name' =>json_encode($goods_t_name),
                'goods_t_num'  =>json_encode($goods_t_num),
                'day'        => $day,
                'sku_ids'    => $sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'progress'    => $progress,
                'zhongdian'   =>$zhongdian,
                'jiankangguanli'=>$jiankangguanli,
                'ccheck'    => $ccheck,
                'fenji'    => $fenji,
                'c_fenji'    => $c_fenji,
                'service_money'    => $service_money,
            ];
            $attr_id   = model("order_attr")->add($order_attr);
            //存在编辑订单的情况
            if ($edit_order_id) {
                $edit_order_info = model("order")->getInfo([["order_id", "=", $edit_order_id]], "*");
                model("order")->update(['is_delete' =>'1'], [['order_no', '=', $edit_order_info['order_no']]]);
            }
            //分装模板详细
            $fen_goods_name = [];
            $fen_goods_num  = [];
            $fen_sku_ids    = [];
            foreach ($goods_name as $item=>$iv){
                $ivv = explode('_',$iv);
                if (in_array($ivv[1],$fen_ye)){
                    continue;
                }
                $fen_sku_ids[] = $ivv[1];
                $fen_goods_name[] = $iv;
                $fen_goods_num[] = $goods_num[$item];
            }
            $fen_sku_ids = implode(',',$fen_sku_ids);
            $attr_fenzhuang       = [
                'order_id' => $res['data'],
                'site_id' => $this->site_id,
                'goods_name' =>json_encode(array_merge($fen_goods_name,$goods_t_name)),
                'goods_num'  =>json_encode(array_merge($fen_goods_num,$goods_t_num)),
                'goods_t_name' =>json_encode($goods_t_name),
                'goods_t_num'  =>json_encode($goods_t_num),
                'day'        => $day,
                'sku_ids'    => $fen_sku_ids,
                'earlymoning_time'    => $earlymoning_time,
                'moning_time'    => $moning_time,
                'aftnoon_time'    => $aftnoon_time,
                'canjian_time'    => $canjian_time,
                'night_time'    => $night_time,
                'sleep_time'    => $sleep_time,
                'progress'    => $progress,
                'zhongdian'   =>$zhongdian,
                'jiankangguanli'=>$jiankangguanli,
                'ccheck'    => $ccheck,
                'fenji'    => $fenji,
                'c_fenji'    => $c_fenji,
                'service_money'    => $service_money,
            ];
            $attr_id   = model("order_attr_fenzhuang")->add($attr_fenzhuang);

            //请求生成外部支付订单
            $order_extend = model('order')->getInfo([['out_trade_no', "=",$res['data']]]);
            $member = model('member')->getInfo([['member_id', "=", $order_extend['member_id']]]);
            $doctor = model('user')->getInfo([['uid', "=", $member['user_id']]]);
            $doctor_n = $doctor?$doctor['username']:'';
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctorList();
            $jigou = '';
            if ($user_bpdotocr[$member['user_id']]['bp']){
                $jigou = $jigou.$user_bpdotocr[$member['user_id']]['bp'];
            }
            if ($guding_detail){
                $province = model("area")->getInfo([['id', '=', $province_id]]);
                $city = model("area")->getInfo([['id', '=', $city_id]]);
                $district = model("area")->getInfo([['id', '=', $district_id]]);
                $ordersn = 'CF'.date('YmdHis'). mt_rand(1000, 9999);
                $totalprice = $guding_total + $data['service_money'] + $data['invoice_money'] + $data['promotion_money'];
                $address1 = [];
                $address1['realname']=$addressname;
                $address1['mobile']=$addressmobile;
                $address1['address']=$address;
                $address1['province']=$province['name'];
                $address1['city']=$city['name'];
                $address1['area']=$district['name'];
                //身份证号，姓名设置为空
                $address1['identitycard'] = '';
                $address1['cardrealname'] = '';
                $order    = array(
                    'uniacid' => 1,
                    'openid' => $member['wx_openid']?$member['wx_openid']:'U230504104271706376',
                    'ordersn' => $ordersn,
                    'price' => $totalprice,
                    'goodsprice'=>$guding_total,
                    'cash' => 0,
                    'discountprice' => 0,
                    'deductprice' => 0,
                    'deductcredit' => 0,
                    'deductenough' => 0,
                    'status' => 0,
                    'paytype' => 0,
                    'transid' => '',
                    'remark' => '分包订单：'.$order_extend['order_no'].' 机构：'.$jigou.' /医生：'.$doctor_n.' /客户：'.$member['username'],
                    'addressid' => 1,
                    'dispatchprice' => $data['service_money'] + $data['invoice_money'] + $data['promotion_money'],
                    'dispatchtype' => 0,
                    'dispatchid' => 0,
                    "storeid" => 0,
                    'carrier' => 0,
                    'createtime' => time(),
                    'isverify' => 0,
                    'verifycode' => '',
                    'virtual' => '',
                    'isvirtual' => 0,
                    'oldprice' => $guding_total,
                    'olddispatchprice' => $data['service_money'] + $data['invoice_money'] + $data['promotion_money'],
                    'address' => '',
                    "couponid" => $order_extend['order_no'],
                    "couponprice" => 0,
                    "cangku" => 3
                );
                $order['address'] = serialize($address1);
                $order['yushou'] = 1;
                $orderid = Db::connect('v3')->table('baijiacms_eshop_order')->insertGetId($order);
                foreach ($guding_detail as $ky=> $iv) {

                        $order_goods_detail = Db::connect('v3')->table('baijiacms_eshop_goods')->where('productsn','=',$iv['sku_no'])->find();
                        $totalprice = $iv['price']*$iv['fen_ye_num'];
                        $order_goods = array(
                            'uniacid' => 1,
                            'orderid' => $orderid,
                            'goodsid' => $order_goods_detail['id'],
                            'price' => $totalprice,
                            'total' => $iv['fen_ye_num'],
                            'optionid' => 0,
                            'createtime' => time(),
                            'optionname' => '',
                            'goodssn' => 0,
                            'productsn' => $order_goods_detail['productsn'],
                            "realprice" => $totalprice,
                            "oldprice" => $totalprice,
                            "openid" => $member['wx_openid']?$member['wx_openid']:'U230504104271706376',
                        );
                        Db::connect('v3')->table('baijiacms_eshop_order_goods')->insert($order_goods);
                        Db::connect('v3')->table('baijiacms_eshop_order_taocan')->insert($order_goods);

                }
                $res = model("order")->update(['extend_id' =>$ordersn], [['order_id', '=', $order_extend['order_id']]]);
            }

            if ($fen_ye_detail){
                $province = model("area")->getInfo([['id', '=', $province_id]]);
                $city = model("area")->getInfo([['id', '=', $city_id]]);
                $district = model("area")->getInfo([['id', '=', $district_id]]);
                $ordersn = 'CF'.date('YmdHis'). mt_rand(1000, 9999);
                $totalprice = $fen_ye_total;
                $address1 = [];
                $address1['realname']=$addressname;
                $address1['mobile']=$addressmobile;
                $address1['address']=$address;
                $address1['province']=$province['name'];
                $address1['city']=$city['name'];
                $address1['area']=$district['name'];
                //身份证号，姓名设置为空
                $address1['identitycard'] = '';
                $address1['cardrealname'] = '';
                $order    = array(
                    'uniacid' => 1,
                    'openid' => $member['wx_openid']?$member['wx_openid']:'U230504104271706376',
                    'ordersn' => $ordersn,
                    'price' => $totalprice,
                    'goodsprice'=>$totalprice,
                    'cash' => 0,
                    'discountprice' => 0,
                    'deductprice' => 0,
                    'deductcredit' => 0,
                    'deductenough' => 0,
                    'status' => 0,
                    'paytype' => 0,
                    'transid' => '',
                    'remark' => '分包订单：'.$order_extend['order_no'].' 机构：'.$jigou.' /医生：'.$doctor_n.' /客户：'.$member['username'],
                    'addressid' => 1,
                    'dispatchprice' => 0,
                    'dispatchtype' => 0,
                    'dispatchid' => 0,
                    "storeid" => 0,
                    'carrier' => 0,
                    'createtime' => time(),
                    'isverify' => 0,
                    'verifycode' => '',
                    'virtual' => '',
                    'isvirtual' => 0,
                    'oldprice' => $totalprice,
                    'olddispatchprice' => 0,
                    'address' => '',
                    "couponid" => $order_extend['order_no'],
                    "couponprice" => 0,
                    "cangku" => 0
                );
                $order['address'] = serialize($address1);
                $order['yushou'] = 1;
                $orderid = Db::connect('v3')->table('baijiacms_eshop_order')->insertGetId($order);
                foreach ($fen_ye_detail as $ky=> $iv) {
                    $order_goods_detail = Db::connect('v3')->table('baijiacms_eshop_goods')->where('productsn','=',ltrim($iv['sku_no'], 'P'))->find();
                    $totalprice = $iv['price']*$iv['fen_ye_num'];
                    $order_goods = array(
                        'uniacid' => 1,
                        'orderid' => $orderid,
                        'goodsid' => $order_goods_detail['id'],
                        'price' => $totalprice,
                        'total' => $iv['fen_ye_num'],
                        'optionid' => 0,
                        'createtime' => time(),
                        'optionname' => '',
                        'goodssn' => 0,
                        'productsn' => $order_goods_detail['productsn'],
                        "realprice" => $totalprice,
                        "oldprice" => $totalprice,
                        "openid" => $member['wx_openid']?$member['wx_openid']:'U230504104271706376',
                    );
                    Db::connect('v3')->table('baijiacms_eshop_order_goods')->insert($order_goods);
                    Db::connect('v3')->table('baijiacms_eshop_order_taocan')->insert($order_goods);
                }
                $res = model("order")->update(['invoice_full_address' =>$ordersn], [['order_id', '=', $order_extend['order_id']]]);
            }

            //存在编辑订单的情况
            /*if ($edit_order_id){
                if ($edit_order_info["order_status"]==0){
                    $order_common_model = new OrderCommonModel();
                    $resultt             = $order_common_model->orderClose($edit_order_id);
                }
                $res = model("order")->update(['order_no' =>$edit_order_info['order_no']], [['order_id', '=', $order_extend['order_id']]]);

            }*/

            return success(0, 'SUCCESS',['order_id'=>$order_extend['order_id'],'ordersn'=>$ordersn]);

        }
        //参考模板
        $cankao = input("cankao", 0);
        //获取会员列表
        $condition = [];
        //医生组获取自己的订单
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["user_id", "in", $datu];
                }else{
                    $condition[] = ["user_id", "=", $this->uid];
                }

            }else{
                $condition[] = ["user_id", "=", $this->uid];
            }
        }
        $memberlist =  model('member')->pageList($condition, '*', 'member_id desc', 1, 0);
        $memberlist = $memberlist['list'];
        foreach($memberlist as $key=>$vo){
            if ($vo['username']!=$vo['nickname']){
                $memberlist[$key]['username'] = $vo['username'].' 昵称:'.$vo['nickname'];
            }
        }
        $this->assign("member_list", $memberlist);
        //获取处方模板列表
        $attrlist =  model('order_attr_class')->pageList(['uid'=>$this->uid,'guding'=>0], '*', 'class_id desc', 1, 0);
        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctoruidList();
        $attrgudinglist = [];
        if ($user_bpdotocr[$this->uid]['bp']){
            $attrgudinglist =  model('order_attr_class')->pageList(['uid'=>$user_bpdotocr[$this->uid]['bp'],'guding'=>1], '*', 'class_id desc', 1, 0);
            $attrgudinglist = $attrgudinglist['list'];
            foreach($attrgudinglist as $key=>$vo){
                $attrgudinglist[$key]['class_name'] = '(推荐)'.$vo['class_name'];
            }
        }
        //获取机构推荐模版
        $attrlist = $attrlist['list'];
        $this->assign("attr_list", $attrlist);
        //机构模板
        $this->assign("attr_list2", $attrgudinglist);
        //系统模板
        $attrxitonglist =  model('order_attr_class')->pageList(['uid'=>1,'guding'=>1], '*', 'class_id desc', 1, 0);
        $this->assign("attr_list3", $attrxitonglist['list']);

        //查询省级数据列表
        $address_model = new AddressModel();
        $list          = $address_model->getAreaList([["pid", "=", 0], ["level", "=", 1]]);
        $this->assign("province_list", $list["data"]);

        //获取订单常用收货地址
        $order_deverys = model('order')->getList([['admin_uid', "=", $this->uid]],"count('address') as num,address,name,mobile",'num desc','','','address,name,mobile');
        $this->assign("order_deverys", $order_deverys);
        //获取会员默认收货地址
        $config_model = new ConfigModel();
        $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
        $this->assign("service_money", $order_event_time_config['data']['value']['service_money']);

        if ($user_bpdotocr[$this->uid]['bp']){
            $user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$this->uid]['bp']]]);
        }
        if ($user['shouhuodizhi']==1){
            $member_id = $user['member_id'];
        }else{
            $member_id = 0;
        }
        $this->assign("guding_member_id", $member_id);

        $this->assign("discount_info", []);
        $this->assign("cankao", $cankao);
        return $this->fetch('order/manuak');
    }

    public function aii(){
        if (request()->isAjax()) {
            $data = [
                'update_time'   => time()
            ];
            model('user')->update($data, [['uid', "=", $this->uid]]);
        }else{
            $userdetail = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($userdetail['update_time']>$userdetail['login_time']){
                $show = 0;
            }else{
                $show = 1;
            }
            $this->assign("show", $show);
            $this->assign("user", $this->uid);
            return $this->fetch('order/aii');
        }
    }

    public function ai(){
        $class_name       = input('class_name', 'AI智能处方');
        $sort             = input('sort', 0);
        $goods = input('goods','');
        $goods_detail = explode('_',$goods);
        $goods_name = $goods_num=$sku_ids=[];
        foreach ($goods_detail as $key=>$vo){
            $vo_detail = explode('|',$vo);
            $goodssku = model('goods_sku')->getInfo([['goods_id','=',$vo_detail['1']]], '*');
            $goods_name[]=$vo_detail[0].'_'.$goodssku['sku_id'];
            $goods_num[] = $vo_detail[2];
            $sku_ids[]=$goodssku['sku_id'];
        }

        $earlymoning_time = input('earlymoning_time', '');
        $moning_time = input('moning_time', '');
        $aftnoon_time = input('aftnoon_time', '');
        $canjian_time = input('canjian_time', '');
        $night_time = input('night_time', '');
        $sleep_time = input('sleep_time', '');
        $price = input('price', '');
        $day = input('day',30);
        $sku_ids = implode(',',$sku_ids);
        $data             = [
            'uid'        => 0,
            'site_id'    => $this->site_id,
            'class_name' => $class_name,
            'sort'       => $sort,
            'goods_name' =>json_encode($goods_name),
            'goods_num'  =>json_encode($goods_num),
            'day'        => $day,
            'sku_ids'    => $sku_ids,
            'earlymoning_time'    => $earlymoning_time,
            'moning_time'    => $moning_time,
            'aftnoon_time'    => $aftnoon_time,
            'canjian_time'    => $canjian_time,
            'night_time'    => $night_time,
            'sleep_time'    => $sleep_time,
            'price'    => floatval($price),
        ];
        $res              = model("order_attr_class")->add($data);
        header('Location:/shop/order/manuak.html?cankao='.$res);
    }

    /**
     * 快递订单列表
     */
    public function lists()
    {
        $order_label_list   = array(
            "order_no"     => "订单号",
            "member_name"  => "客户姓名",
            "admin_name"   => "医生姓名",
            "name"         => "收货人姓名",
            "delivery_no"  => "顺丰号",
        );
        $order_status       = input("order_status", "");//订单状态
        $order_name         = input("order_name", '');
        $pay_type           = input("pay_type", '');
        $order_from         = input("order_from", '');
        $userlist           = input("userlist", '');
        $start_time         = input("start_time", '');
        $end_time           = input("end_time", '');
        $dstart_time         = input("dstart_time", '');
        $dend_time           = input("dend_time", '');
        $pstart_time     = input("pstart_time", '');
        $pend_time       = input("pend_time", '');
        $order_label        = !empty($order_label_list[input("order_label")]) ? input("order_label") : "";
        $search_text        = input("search", '');
        $promotion_type     = input("promotion_type", '');//订单类型
        $order_type         = input("order_type", 'all');//营销类型
        $order_common_model = new OrderCommonModel();
        if (request()->isAjax()) {
            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);
            $condition  = [
//                ["order_type", "=", 1],
                ["site_id", "=", $this->site_id],
                ['is_delete', '=', 0]
            ];
            //订单状态
            if ($order_status != "") {
                $condition[] = ["order_status", "=", $order_status];
            }
            //订单内容 模糊查询
            if ($order_name != "") {
                $condition[] = ["order_name", 'like', "%$order_name%"];
            }
            //订单来源
            if ($order_from != "") {
                $condition[] = ["order_from", "=", $order_from];
            }
            //机构
            if ($userlist != "") {
                $user_model = new UserModel();
                $datau      = $user_model->bpdoctordata();
                if (isset($datau[$userlist])){
                    $datu       = array_merge($datau[$userlist]['bp'],$datau[$userlist]['doctor']);
                    $datu       = array_merge($datu,$datau[$userlist]['patient']);
                    if ($datu){
                        $condition[] = ["admin_uid", "in", $datu];
                    }else{
                        $condition[] = ["admin_uid", "=", $userlist];
                    }

                }
            }
            //订单支付
            if ($pay_type != "") {
                $condition[] = ["pay_type", "=", $pay_type];
            }
            //订单类型
            if ($order_type != 'all') {
                $condition[] = ["order_type", "=", $order_type];
            }
            //营销类型
            if ($promotion_type != "") {
                if ($promotion_type == 'empty') {
                    $condition[] = ["promotion_type", "=", ''];
                } else {
                    $condition[] = ["promotion_type", "=", $promotion_type];
                }
            }
            if (!empty($start_time) && empty($end_time)) {
                $condition[] = ["create_time", ">=", date_to_time($start_time)];
            } elseif (empty($start_time) && !empty($end_time)) {
                $condition[] = ["create_time", "<=", date_to_time($end_time)];
            } elseif (!empty($start_time) && !empty($end_time)) {
                $condition[] = ['create_time', 'between', [date_to_time($start_time), date_to_time($end_time)]];
            }

            if (!empty($dstart_time) && empty($dend_time)) {
                $condition[] = ["delivery_time", ">=", date_to_time($dstart_time)];
            } elseif (empty($dstart_time) && !empty($dend_time)) {
                $condition[] = ["delivery_time", "<=", date_to_time($end_time)];
            } elseif (!empty($dstart_time) && !empty($dend_time)) {
                $condition[] = ['delivery_time', 'between', [date_to_time($dstart_time), date_to_time($dend_time)]];
            }
            if (!empty($pstart_time) && empty($pend_time)) {
                $condition[] = ["pay_time", ">=", date_to_time($pstart_time)];
            } elseif (empty($pstart_time) && !empty($pend_time)) {
                $condition[] = ["pay_time", "<=", date_to_time($pend_time)];
            } elseif (!empty($pstart_time) && !empty($pend_time)) {
                $condition[] = ['pay_time', 'between', [date_to_time($pstart_time), date_to_time($pend_time)]];
            }
            if ($search_text != "") {
                if ($order_label == 'member_name'){
                    $member = model('member')->getList([['username', "like", "%$search_text%"]]);
                    if ($member){
                        $condition[] = ["member_id", "in", array_column($member,'member_id')];
                    }
                }elseif ($order_label == 'admin_name'){
                    $member = model('user')->getList([['username', "like", "%$search_text%"]]);
                    if ($member){
                        $condition[] = ["admin_uid", "in", array_column($member,'uid')];
                    }
                }else {
                    $condition[] = [$order_label, 'like', "%$search_text%"];
                }
            }
            //医生组获取自己的订单
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($user['group_id'] == 2){
                $user_model = new UserModel();
                $datau      = $user_model->bpdoctordata();
                if (isset($datau[$this->uid])){
                    $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                    $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                    if ($datu){
                        $condition[] = ["admin_uid", "in", $datu];
                    }else{
                        $condition[] = ["admin_uid", "=", $this->uid];
                    }

                }else{
                    $condition[] = ["admin_uid", "=", $this->uid];
                }
            }
            //仓库管理袁
            if ($user['group_id'] == 3){
                $condition[] = ["order_status", "<>", 0];
            }
            $order_by = 'create_time desc';
            //排序
            if($order_status==1){
                $order_by = 'pay_time asc';
            }
            $list = $order_common_model->getOrderPageList($condition, $page_index, $page_size, $order_by,'*',$this->uid);
            return $list;
        } else {

            $order_type_list = $order_common_model->getOrderTypeStatusList();
            $this->assign("order_type_list", $order_type_list);
            $this->assign("order_label_list", $order_label_list);
            $order_model       = new OrderModel();
            $order_status_list = $order_model->order_status;
            $this->assign("order_status_list", $order_status_list);//订单状态

            //订单来源 (支持端口)
            $order_from = Config::get("app_type");
            $this->assign('order_from_list', $order_from);

            $pay_type = $order_common_model->getPayType();
            $this->assign("pay_type_list", $pay_type);

            //营销活动类型
            $order_promotion_type = event('OrderPromotionType');
            $this->assign("promotion_type", $order_promotion_type);
            $this->assign("http_type", get_http_type());

            $userlist = model('user')->getList([['type_id','=',1]], '*');
            $this->assign('userlist', $userlist);

            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            $this->assign('user_type', ($user['type_id']>0?0:1));
            $this->assign('user', $user);

            return $this->fetch('order/lists');
        }

    }

    /**
     * 编辑订单
     */
    public function editl()
    {
        //查找订单信息
        $order_id            = input("order_id", 0);
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];
        $this->assign("order_detail", $order_detail);
        //查找订单模板
        $attr_class_info = model('order_attr')->getInfo([['order_id', '=', $order_detail['out_trade_no']]], '*');
        $this->assign("attr_class_info", $attr_class_info);

        //获取会员列表
        $memberlist =  model('member')->pageList(['user_id'=>$this->uid], '*', 'member_id desc', 1, 0);
        $memberlist = $memberlist['list'];
        foreach($memberlist as $key=>$vo){
            if ($vo['username']!=$vo['nickname']){
                $memberlist[$key]['username'] = $vo['username'].' 昵称:'.$vo['nickname'];
            }
        }
        $this->assign("member_list", $memberlist);
        //获取处方模板列表
        $attrlist =  model('order_attr_class')->pageList(['uid'=>$this->uid,'guding'=>0], '*', 'class_id desc', 1, 0);
        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctoruidList();
        $attrgudinglist = [];
        if ($user_bpdotocr[$this->uid]['bp']){
            $attrgudinglist =  model('order_attr_class')->pageList(['uid'=>$user_bpdotocr[$this->uid]['bp'],'guding'=>1], '*', 'class_id desc', 1, 0);
            $attrgudinglist = $attrgudinglist['list'];
            foreach($attrgudinglist as $key=>$vo){
                $attrgudinglist[$key]['class_name'] = '(推荐)'.$vo['class_name'];
            }
        }
        //获取机构推荐模版
        $attrlist = $attrlist['list'];
        $this->assign("attr_list", $attrlist);
        //机构模板
        $this->assign("attr_list2", $attrgudinglist);
        //系统模板
        $attrxitonglist =  model('order_attr_class')->pageList(['uid'=>1,'guding'=>1], '*', 'class_id desc', 1, 0);
        $this->assign("attr_list3", $attrxitonglist['list']);

        //查询省级数据列表
        $address_model = new AddressModel();
        $list          = $address_model->getAreaList([["pid", "=", 0], ["level", "=", 1]]);
        $this->assign("province_list", $list["data"]);
        //获取订单常用收货地址
        $order_deverys = model('order')->getList([['admin_uid', "=", $this->uid]],"count('address') as num,address,name,mobile",'num desc','','','address,name,mobile');
        $this->assign("order_deverys", $order_deverys);
        //获取会员默认收货地址
        $config_model = new ConfigModel();
        $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
        $this->assign("service_money", $order_event_time_config['data']['value']['service_money']);

        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user_bpdotocr[$this->uid]['bp']){
            $user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$this->uid]['bp']]]);
        }
        if ($user['shouhuodizhi']==1){
            $member_id = $user['member_id'];
        }else{
            $member_id = 0;
        }
        $this->assign("guding_member_id", $member_id);

        $this->assign("discount_info", []);
        return $this->fetch('order/editmanual');
    }

    /**
     * 编辑订单
     */
    public function editk()
    {
        //查找订单信息
        $order_id            = input("order_id", 0);
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];
        $this->assign("order_detail", $order_detail);
        //查找订单模板
        $attr_class_info = model('order_attr')->getInfo([['order_id', '=', $order_detail['out_trade_no']]], '*');
        $this->assign("attr_class_info", $attr_class_info);

        //获取会员列表--这里应该获取原订单uid
        $memberlist =  model('member')->pageList(['user_id'=>$this->uid], '*', 'member_id desc', 1, 0);
        $memberlist = $memberlist['list'];
        foreach($memberlist as $key=>$vo){
            if ($vo['username']!=$vo['nickname']){
                $memberlist[$key]['username'] = $vo['username'].' 昵称:'.$vo['nickname'];
            }
        }
        $this->assign("member_list", $memberlist);
        //获取处方模板列表
        $attrlist =  model('order_attr_class')->pageList(['uid'=>$this->uid,'guding'=>0], '*', 'class_id desc', 1, 0);
        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctoruidList();
        $attrgudinglist = [];
        if ($user_bpdotocr[$this->uid]['bp']){
            $attrgudinglist =  model('order_attr_class')->pageList(['uid'=>$user_bpdotocr[$this->uid]['bp'],'guding'=>1], '*', 'class_id desc', 1, 0);
            $attrgudinglist = $attrgudinglist['list'];
            foreach($attrgudinglist as $key=>$vo){
                $attrgudinglist[$key]['class_name'] = '(推荐)'.$vo['class_name'];
            }
        }
        //获取机构推荐模版
        $attrlist = $attrlist['list'];
        $this->assign("attr_list", $attrlist);
        //机构模板
        $this->assign("attr_list2", $attrgudinglist);
        //系统模板
        $attrxitonglist =  model('order_attr_class')->pageList(['uid'=>1,'guding'=>1], '*', 'class_id desc', 1, 0);
        $this->assign("attr_list3", $attrxitonglist['list']);

        //查询省级数据列表
        $address_model = new AddressModel();
        $list          = $address_model->getAreaList([["pid", "=", 0], ["level", "=", 1]]);
        $this->assign("province_list", $list["data"]);
        //获取订单常用收货地址
        $order_deverys = model('order')->getList([['admin_uid', "=", $this->uid]],"count('address') as num,address,name,mobile",'num desc','','','address,name,mobile');
        $this->assign("order_deverys", $order_deverys);
        //获取会员默认收货地址
        $config_model = new ConfigModel();
        $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
        $this->assign("service_money", $order_event_time_config['data']['value']['service_money']);

        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user_bpdotocr[$this->uid]['bp']){
            $user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$this->uid]['bp']]]);
        }
        if ($user['shouhuodizhi']==1){
            $member_id = $user['member_id'];
        }else{
            $member_id = 0;
        }
        $this->assign("guding_member_id", $member_id);

        $this->assign("discount_info", []);

        //是否编辑
        $edit = input("edit", 0);
        $this->assign("edit_order_id", $edit?$order_id:'');
        return $this->fetch('order/editmanuak');
    }
    /**
     * 快递订单详情
     */
    public function detail()
    {
        $order_id            = input("order_id", 0);
        $weilai            = input("weilaiyisheng", 0);
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];
        //补全订单地址信息
        if (!$order_detail['full_address']){
            $province = model("area")->getInfo([['id', '=', $order_detail['province_id']]]);
            $city = model("area")->getInfo([['id', '=', $order_detail['city_id']]]);
            $district = model("area")->getInfo([['id', '=', $order_detail['district_id']]]);
            $order_detail['full_address'] = $province['name'].$city['name'].$district['name'].$order_detail['address'];
        }
        $this->assign("order_detail", $order_detail);
        //增加订单运单照片
        $express_images = [];
        if ($order_detail['delivery_no']){
            $express_images = model('order_deveryimages')->getList([['express_no','=',$order_detail['delivery_no']]], $field = true, $order = '', $alias = 'a', $join = [], $group = '');
        }
        $width = '30%';
        if (is_array($express_images) && count($express_images)>6){
            $width = '21%';
        }
        if (is_array($express_images) && count($express_images)>9){
            $width = '17%';
        }
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 3 || $user['group_id'] == 1){
            $this->assign("isdelete", 1);
        }else{
            $this->assign("isdelete", 0);
        }
        $this->assign("width", $width);
        $this->assign("express_images", $express_images);
        switch ($order_detail["order_type"]) {
            case 1 :
                $template = "order/detail";
                break;
            case 2 :
                $template = "storeorder/detail";
                break;
            case 3 :
                $template = "localorder/detail";
                break;
            case 4 :
                $template = "virtualorder/detail";
                break;
        }

        $this->assign("http_type", get_http_type());
        if ($weilai==1){
            return $this->fetch('order/detail_w');
        }
        return $this->fetch($template);
    }

    /**
     * 订单设置
     */
    public function config()
    {
        $config_model = new ConfigModel();
        if (request()->isAjax()) {
            //订单事件时间设置数据
            $order_event_time_config_data = [
                'auto_close'         => input('order_auto_close_time', 0),//订单未付款自动关闭时间 数字 单位(天)
                'auto_take_delivery' => input('order_auto_take_delivery_time', 0),//订单发货后自动收货时间 数字 单位(天)
                'auto_complete'      => input('order_auto_complete_time', 0),//订单收货后自动完成时间 数字 单位(天)

                'invoice_status'  => input('invoice_status', 0),
                'invoice_rate'    => input('invoice_rate', 0),
                'invoice_content' => input('invoice_content', ''),
                'invoice_money'   => input('invoice_money', 0),
                'service_money'   => input('service_money', 0),
                'waihe_money'     => input('waihe_money', 0),
                'fenji_money'     => input('fenji_money', 0),
            ];
            $res                          = $config_model->setOrderEventTimeConfig($order_event_time_config_data, $this->site_id, $this->app_module);
            return $res;
        } else {

            //订单事件时间设置
            $order_event_time_config = $config_model->getOrderEventTimeConfig($this->site_id, $this->app_module);
            $this->assign('order_event_time_config', $order_event_time_config['data']['value']);
            return $this->fetch('order/config');
        }
    }

    /**
     * 订单关闭
     * @return mixed
     */
    public function close()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            //医生不能关闭订单
            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            $order_info = model("order")->getInfo([["order_id", "=", $order_id]], "coupon_id,pay_status,member_id,is_lock,balance_money,order_no,mobile,order_status,site_id");
            if ($user['group_id'] == 2 && $order_info["order_status"]>0){
                return $this->error('订单已确认，不能删除订单。若要关闭订单，请联系管理员');
            }
            $order_common_model = new OrderCommonModel();
            $result             = $order_common_model->orderClose($order_id);
            return $result;
        }
    }

    /**
     * 订单调价
     * @return mixed
     */
    public function adjustPrice()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            $adjust_money       = input("adjust_money", 0);
            $delivery_money     = input("delivery_money", 0);
            $order_common_model = new OrderCommonModel();
            $result             = $order_common_model->orderAdjustMoney($order_id, $adjust_money, $delivery_money);
            return $result;
        }
    }

    /**
     * 订单发货
     * @return mixed
     */
    public function delivery()
    {
        if (request()->isAjax()) {

            $order_model = new OrderModel();
            $data        = array(
                "type"               => input('type', 'manual'),//发货方式（手动发货、电子面单）
                "order_goods_ids"    => input("order_goods_ids", ''),//商品id
                "express_company_id" => input("express_company_id", 0),//物流公司
                "delivery_no"        => input("delivery_no", ''),//快递单号
                "order_id"           => input("order_id", 0),//订单id
                "delivery_type"      => input("delivery_type", 0),//是否需要物流
                "site_id"            => $this->site_id,
                "template_id"        => input('template_id', 0)//电子面单模板id
            );

            $result = $order_model->orderGoodsDelivery($data);
            return $result;
        } else {
            $order_id           = input("order_id", 0);
            $delivery_status    = input("delivery_status", '');
            $order_common_model = new OrderCommonModel();
            $condition          = array(
                ["order_id", "=", $order_id],
                ["site_id", "=", $this->site_id],
            );
            if ($delivery_status === '') {
                $condition[] = ["delivery_status", "=", $delivery_status];
            }
            $field                   = "order_goods_id, order_id, site_id, site_name, sku_name, sku_image, sku_no, is_virtual, price, cost_price, num, goods_money, cost_money, delivery_status, delivery_no, goods_id, delivery_status_name";
            $order_goods_list_result = $order_common_model->getOrderGoodsList($condition, $field, '', null, "delivery_no");
            $order_goods_list        = $order_goods_list_result["data"];
            $this->assign("order_goods_list", $order_goods_list);
            return $this->fetch("order/delivery");
        }
    }

    /**
     * 获取订单项列表
     */
    public function getOrderGoodsList()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            $delivery_status    = input("delivery_status", '');
            $order_common_model = new OrderCommonModel();
            $condition          = array(
                ["order_id", "=", $order_id],
                ["site_id", "=", $this->site_id],
                ["refund_status", "<>", 3],
            );
            if ($delivery_status != '') {
                $condition[] = ["delivery_status", "=", $delivery_status];
            }
            $field  = "order_goods_id, order_id, site_id, sku_name, sku_image, sku_no, is_virtual, price, cost_price, num, goods_money, cost_money, delivery_status, delivery_no, goods_id, delivery_status_name";
            $result = $order_common_model->getOrderGoodsList($condition, $field, '', null, "");
            return $result;
        }
    }

    /**
     * 订单修改收货地址
     * @return mixed
     */
    public function editAddress()
    {
        $order_id = input("order_id", 0);
        if (request()->isAjax()) {
            $order_model  = new OrderModel();
            $province_id  = input("province_id");
            $city_id      = input("city_id");
            $district_id  = input("district_id");
            $community_id = input("community_id",0);
            $address      = input("address");
            $full_address = input("full_address");
            $longitude    = input("longitude");
            $latitude     = input("latitude");
            $mobile       = input("mobile");
            $telephone    = input("telephone");
            $name         = input("name");
            $data         = array(
                "province_id"  => $province_id,
                "city_id"      => $city_id,
                "district_id"  => $district_id,
                "community_id" => $community_id,
                "address"      => $address,
                "full_address" => $full_address,
                "longitude"    => $longitude,
                "latitude"     => $latitude,
                "mobile"       => $mobile,
                "telephone"    => $telephone,
                "name"         => $name,
            );
            $condition    = array(
                ["order_id", "=", $order_id],
                ["site_id", "=", $this->site_id]
            );
            $result       = $order_model->orderAddressUpdate($data, $condition);
            //重新生成运单
            $order_common_model  = new OrderCommonModel();
            $order_detail_result = $order_common_model->getOrderDetail($order_id);
            $order_detail        = $order_detail_result["data"];
            if($order_detail['pay_status']==1) {
                $shunfeng_g[] = [
                    "amount" => 100,
                    "count" => 1,
                    "name" => '保健品',
                    "unit" => "瓶",
                    "weight" => '20'
                ];
                //收货地址
                $province = model('area')->getInfo([['id', '=', $province_id]]);
                $city = model('area')->getInfo([['id', '=', $city_id]]);
                $county = model('area')->getInfo([['id', '=', $district_id]]);
                $serviceCode = "EXP_RECE_CREATE_ORDER";
                $cargoDetails = $shunfeng_g;
                $contactInfoList = [];
                $contactInfoList[] = [
                    "address" => $address,
                    "city" => $city["name"],
                    "contact" => $name,
                    "contactType" => 2,
                    "county" => $county["name"],
                    "mobile" => $mobile,
                    "province" => $province["name"]
                ];
                $contactInfoList[] = [
                    "address" => "xxxxxxxxxxxxxxxxx",
                    "city" => "聊城市",
                    "contact" => "马福*",
                    "county" => "东昌府区",
                    "contactType" => 1,
                    "mobile" => "16606345016",
                    "company" => "",
                    "province" => "山东省"
                ];
                $msgData = [
                    'cargoDetails' => $cargoDetails,
                    'contactInfoList' => $contactInfoList,
                    'expressTypeId' => 2,
                    'language' => 'zh-CN',
                    'monthlyCard' => '5313549825',
                    'orderId' => time().'0001',
                    'payMethod' => 1
                ];
                $msgData = json_encode($msgData);
                $shun = $order_common_model->shunfeng($serviceCode, $msgData);
                if ($shun['status'] == 1) {
                    //保存快递编号
                    $res = model("order")->update(['delivery_no' => $shun['waybillNo'], 'pdf' => ''], [['order_id', '=', $order_detail['order_id']]]);
                    //测试顺丰下单
                    $serviceCode = "COM_RECE_CLOUD_PRINT_WAYBILLS";
                    $msgData = '{
    "templateCode": "fm_150_standard_QZKJPjIXU2Q",
    "version":"2.0",
    "fileType":"pdf",
    "documents": [{
    "masterWaybillNo": "' . $shun['waybillNo'] . '"
    }]
}';
                    $order_common_model->shunfeng($serviceCode, $msgData);
                } else {
                    return $this->error('无法生成运单' . $shun['msg']);
                }
            }
            return $result;
        }
    }

    /**
     * 获取订单信息
     */
    public function getOrderInfo()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            $order_money        = input("order_money",0);
            $order_extend = model('order')->getInfo([['order_id', "=",$order_id]]);
            $pay_weixin='';
            $pay_aliay='';
            //订单未支付重新获取支付信息
            if ($order_money>1){
                $post_url = 'http://www.metawordtech.com/api/goods_baojing.php';
                $post_data = ['fen_ye_order'=>$order_extend['invoice_full_address'],'gu_order'=>$order_extend['extend_id']];
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $post_url);
                curl_setopt($ch, CURLOPT_HEADER, 0);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

                if ($post_data != '' && !empty($post_data)) {
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
                }
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                $result = curl_exec($ch);
                curl_close($ch);
                $result = json_decode($result,true);
                $pay_weixin = $result['gu_order'];
                $pay_aliay = $result['fen_ye_order'];
            }
            $order_common_model = new OrderCommonModel();
            $condition          = array(
                ["order_id", "=", $order_id],
                ["site_id", "=", $this->site_id],
            );
            $result             = $order_common_model->getOrderInfo($condition);

            $result['data']['pay_weixin'] = $pay_weixin;
            $result['data']['pay_aliay'] = $pay_aliay;


            $user = model('user')->getInfo([['uid', "=", $this->uid]]);
            if ($user['group_id'] == 2){
                $result['data']['doctor'] = 1;
            }else{
                $result['data']['doctor'] = 0;
            }
            //如果订单未支付则获取外部支付信息
            /*if ($result['data']['pay_status'] == 0){
                //请求生成外部支付订单
                $post_url = 'http://m.metawordtech.com/api/order_check.php';
                $post_data = ['order_id'=>$result['data']['extend_id']];
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $post_url);
                curl_setopt($ch, CURLOPT_HEADER, 0);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

                if ($post_data != '' && !empty($post_data)) {
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
                }
                curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                $resu = curl_exec($ch);
                curl_close($ch);
                $resu = json_decode($resu,true);
                if ($resu['status'] == 1){
                    $data = [
                        'out_trade_no'=>$result['data']['out_trade_no'],
                        'pay_type'=>$resu['pay_type'],
                    ];
                    $order_common_model = new OrderCommonModel();
                    $resu             = $order_common_model->orderOnlinePay($data);
                    //重新获取信息
                    $result             = $order_common_model->getOrderInfo($condition);
                    $result['data']['pay_weixin'] = urlencode("http://m.metawordtech.com/api/order_weixin.php?osn=".urlencode(base64_encode($result['data']['extend_id'])));
                    $result['data']['pay_aliay'] = urlencode("http://m.metawordtech.com/api/order_aliay.php?osn=".$result['data']['extend_id']);
                }
            }*/
            return $result;
        }
    }

    /**
     * 获取订单 订单项内容
     */
    public function getOrderDetail()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            $order_common_model = new OrderCommonModel();
            $result             = $order_common_model->getOrderDetail($order_id);
            return $result;
        }
    }

    /**
     * 卖家备注
     */
    public function orderRemark()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            $remark             = input("remark", 0);
            $order_common_model = new OrderCommonModel();
            $condition          = array(
                ["order_id", "=", $order_id],
                ["site_id", "=", $this->site_id],
            );
            //区分卖家买家备注
            if ($this->user_info[ "group_id" ] == 1 || $this->user_info[ "group_id" ] == 3){
                $data               = array(
                    "remark_mai" => $remark
                );
            }else{
                $data               = array(
                    "remark" => $remark
                );
            }

            $result             = $order_common_model->orderUpdate($data, $condition);
            return $result;
        }
    }

    /**
     * 订单导出（已订单为主）
     */
    public function exportOrder()
    {
        $order_status   = input("order_status", "");//订单状态
        $order_name     = input("order_name", '');
        $pay_type       = input("pay_type", '');
        $order_from     = input("order_from", '');
        $userlist       = input("userlist", '');
        $start_time     = input("start_time", '');
        $end_time       = input("end_time", '');
        $dstart_time     = input("dstart_time", '');
        $dend_time       = input("dend_time", '');
        $pstart_time     = input("pstart_time", '');
        $pend_time       = input("pend_time", '');
        $order_label    = input("order_label", "");
        $search_text    = input("search", '');
        $promotion_type = input("promotion_type", '');
        $order_type     = input("order_type", '1');

        $condition[] = ["order_type", "=", $order_type];
        $condition[] = ["site_id", "=", $this->site_id];
        $condition[] = ["is_delete", "=", 0];

        //订单状态
        if ($order_status != "") {
            $condition[] = ["order_status", "=", $order_status];
        }
        //订单内容 模糊查询
        if ($order_name != "") {
            $condition[] = ["order_name", 'like', "%$order_name%"];
        }
        //订单来源
        if ($order_from != "") {
            $condition[] = ["order_from", "=", $order_from];
        }
        //机构
        if ($userlist != "") {
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$userlist])){
                $datu       = array_merge($datau[$userlist]['bp'],$datau[$userlist]['doctor']);
                $datu       = array_merge($datu,$datau[$userlist]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $userlist];
                }

            }
        }
        //订单支付
        if ($pay_type != "") {
            $condition[] = ["pay_type", "=", $pay_type];
        }
        //营销类型
        if ($promotion_type != "") {
            if ($promotion_type == 'empty') {
                $condition[] = ["promotion_type", "=", ''];
            } else {
                $condition[] = ["promotion_type", "=", $promotion_type];
            }
        }
        if (!empty($start_time) && !empty($end_time)) {
            $condition[] = ["create_time", "between", [date_to_time($start_time), date_to_time($end_time)]];
        }

        if (!empty($dstart_time) && empty($dend_time)) {
            $condition[] = ["delivery_time", ">=", date_to_time($dstart_time)];
        } elseif (empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ["delivery_time", "<=", date_to_time($dend_time)];
        } elseif (!empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ['delivery_time', 'between', [date_to_time($dstart_time), date_to_time($dend_time)]];
        }

        if (!empty($pstart_time) && empty($pend_time)) {
            $condition[] = ["pay_time", ">=", date_to_time($pstart_time)];
        } elseif (empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ["pay_time", "<=", date_to_time($pend_time)];
        } elseif (!empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ['pay_time', 'between', [date_to_time($pstart_time), date_to_time($pend_time)]];
        }

        if ($search_text != "") {
            if ($order_label == 'member_name'){
                $member = model('member')->getInfo([['username', "like", "%$search_text%"]]);
                if ($member){
                    $condition[] = ["member_id", "=", $member['member_id']];
                }
            }elseif ($order_label == 'admin_name'){
                $member = model('user')->getInfo([['username', "like", "%$search_text%"]]);
                if ($member){
                    $condition[] = ["admin_uid", "=", $member['uid']];
                }
            }else {
                $condition[] = [$order_label, 'like', "%$search_text%"];
            }
        }

        //医生组获取自己的订单
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $this->uid];
                }

            }else{
                $condition[] = ["admin_uid", "=", $this->uid];
            }
        }

        $order_common_model = new OrderCommonModel();

        //接收需要展示的字段
        $input_field = input('field', '');
        $order       = $order_common_model->getOrderList($condition, $input_field, 'order_id desc');
        $header_arr  = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',
            'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ'
        );

        $input_field = explode(',', $input_field);
        //处理数据
        $order_export_model = new OrderExport();
        if (!empty($shop['data'])) {
            $order_list = $order_export_model->handleData($order['data'], $input_field);
        }

        $count = count($input_field);
        // 实例化excel
        $phpExcel = new \PHPExcel();

        $phpExcel->getProperties()->setTitle("订单信息-订单维度");
        $phpExcel->getProperties()->setSubject("订单信息-订单维度");
        //单独添加列名称
        $phpExcel->setActiveSheetIndex(0);

        $field = $order_export_model->order_field;
        for ($i = 0; $i < $count; $i++) {
            $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . '1', $field[$input_field[$i]]);
        }

        if (!empty($order_list)) {
            foreach ($order_list as $k => $v) {
                $start = $k + 2;
                for ($i = 0; $i < $count; $i++) {

                    $phpExcel->getActiveSheet()->setCellValue($header_arr[$i] . $start, $v[$input_field[$i]]);
                }
            }
        }

        // 重命名工作sheet
        $phpExcel->getActiveSheet()->setTitle('订单信息-订单维度');
        // 设置第一个sheet为工作的sheet
        $phpExcel->setActiveSheetIndex(0);
        // 保存Excel 2007格式文件，保存路径为当前路径，名字为export.xlsx
        $objWriter = \PHPExcel_IOFactory::createWriter($phpExcel, 'Excel2007');
        $file      = date('Y年m月d日-订单信息', time()) . '.xlsx';
        $objWriter->save($file);

        header("Content-type:application/octet-stream");

        $filename = basename($file);
        header("Content-Disposition:attachment;filename = " . $filename);
        header("Accept-ranges:bytes");
        header("Accept-length:" . filesize($file));
        readfile($file);
        unlink($file);
        exit;

    }

    /**
     * 订单导出（已订单为主）
     */
    public function exportOrderr()
    {
        $order_status   = input("order_status", "");//订单状态
        $order_name     = input("order_name", '');
        $pay_type       = input("pay_type", '');
        $order_from     = input("order_from", '');
        $userlist           = input("userlist", '');
        $start_time     = input("start_time", '');
        $end_time       = input("end_time", '');
        $dstart_time     = input("dstart_time", '');
        $dend_time       = input("dend_time", '');
        $pstart_time     = input("pstart_time", '');
        $pend_time       = input("pend_time", '');
        $order_label    = input("order_label", "");
        $search_text    = input("search", '');
        $promotion_type = input("promotion_type", '');
        $order_type     = input("order_type", '1');

        $condition[] = ["order_type", "=", $order_type];
        $condition[] = ["site_id", "=", $this->site_id];
        $condition[] = ["is_delete", "=", 0];
        $condition[] = ["order_status", "in", [1,3,4,10]];
        //订单状态
        if ($order_status != "") {
            $condition[] = ["order_status", "=", $order_status];
        }
        //订单内容 模糊查询
        if ($order_name != "") {
            $condition[] = ["order_name", 'like', "%$order_name%"];
        }
        //订单来源
        if ($order_from != "") {
            $condition[] = ["order_from", "=", $order_from];
        }
        //机构
        if ($userlist != "") {
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$userlist])){
                $datu       = array_merge($datau[$userlist]['bp'],$datau[$userlist]['doctor']);
                $datu       = array_merge($datu,$datau[$userlist]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $userlist];
                }

            }
        }
        //订单支付
        if ($pay_type != "") {
            $condition[] = ["pay_type", "=", $pay_type];
        }
        //营销类型
        if ($promotion_type != "") {
            if ($promotion_type == 'empty') {
                $condition[] = ["promotion_type", "=", ''];
            } else {
                $condition[] = ["promotion_type", "=", $promotion_type];
            }
        }
        if (!empty($start_time) && !empty($end_time)) {
            $condition[] = ["create_time", "between", [date_to_time($start_time), date_to_time($end_time)]];
        }

        if (!empty($dstart_time) && empty($dend_time)) {
            $condition[] = ["delivery_time", ">=", date_to_time($dstart_time)];
        } elseif (empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ["delivery_time", "<=", date_to_time($dend_time)];
        } elseif (!empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ['delivery_time', 'between', [date_to_time($dstart_time), date_to_time($dend_time)]];
        }

        if (!empty($pstart_time) && empty($pend_time)) {
            $condition[] = ["pay_time", ">=", date_to_time($pstart_time)];
        } elseif (empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ["pay_time", "<=", date_to_time($pend_time)];
        } elseif (!empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ['pay_time', 'between', [date_to_time($pstart_time), date_to_time($pend_time)]];
        }

        if ($order_label == 'member_name'){
            $member = model('member')->getInfo([['username', "like", "%$search_text%"]]);
            if ($member){
                $condition[] = ["member_id", "=", $member['member_id']];
            }
        }elseif ($order_label == 'admin_name'){
            $member = model('user')->getInfo([['username', "like", "%$search_text%"]]);
            if ($member){
                $condition[] = ["admin_uid", "=", $member['uid']];
            }
        }else {
            $condition[] = [$order_label, 'like', "%$search_text%"];
        }

        //医生组获取自己的订单
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $this->uid];
                }

            }else{
                $condition[] = ["admin_uid", "=", $this->uid];
            }
        }
        $order_common_model = new OrderCommonModel();
        $order_export_model = new OrderExport();

        $field = $order_export_model->orderr_field;
        //接收需要展示的字段
        $ordernum       = $order_common_model->getOrderCount($condition);

        //重写导出方法
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        $columns_title =  [];        //设置好告诉浏览器要下载excel文件的headers
        $columns_key =  [];
        foreach($field as $kk=>$vv){
            $columns_title[]=$vv;
            $columns_key[$kk]='';
        }

        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="'.'订单信息-订单分类'.'.csv"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');        $fp = fopen('php://output', 'a');//打开output流
        //mb_convert_variables('GBK', 'UTF-8', $columns);
        fputcsv($fp, $columns_title);//将数据格式化为CSV格式并写入到output流中
        //获取总数，分页循环处理
        $accessNum = $ordernum['data'];
        $perSize = 1000;
        $pages   = ceil($accessNum / $perSize);

        for($i = 1; $i <= $pages; $i++) {
            $order       = $order_common_model->getOrderPageList($condition,$i,$perSize,'','*',0,'og.sku_id,og.order_id,og.sku_name,og.num,o.goods_chi');
            $order_result = [];
            $result=[];
            if(isset($order['data']['list'])){
                $order['data'] = $order['data']['list'];
            }else{
                $order['data'] = [];
            }
            $sorted = $order_export_model->array_orderby($order['data'], 'bp_name', SORT_DESC,'doctor_name', SORT_DESC,'patient_name', SORT_DESC, 'pay_time', SORT_ASC);
            $order['data'] = $sorted;

            if (!empty($order['data'])) {
                $result = [];
                foreach ($order['data'] as $key=>$vo){
                    foreach($vo['order_goods'] as $kkk=>$vvv){
                        if ($kkk==0){
                            $vo['goods_chi'] = $vvv['goods_chi'];
                            $vo['sku_name'] = $vvv['sku_name'];
                            $vo['num'] = $vvv['num'];
                            $vo['pay_status'] = $vo['pay_status'] == 1 ?'已支付':'未支付';
                            $vo['delivery_status'] = $vo['delivery_status'] == 2 ?'已收货':($vo['delivery_status'] == 1?'已发货':'未发货');
                            $vo['pay_time'] = date('Y-m-d h:i:s',$vo['pay_time']);
                            $vo['delivery_time'] = date('Y-m-d h:i:s',$vo['delivery_time']);
                            $vo['sign_time'] = date('Y-m-d h:i:s',$vo['sign_time']);
                            $result[]=$vo;
                        }else{
                            $voo = [
                                'bp_name'                 => '',
                                'doctor_name'             => '',
                                'patient_name'            => '',
                                'name'                    => '',
                                'order_no'                => '',
                                'site_name'               => '',
                                'order_name'              => '',
                                'order_from_name'         => '',
                                'order_type_name'         => '',
                                'order_promotion_name'    => '',
                                'out_trade_no'            => '',
                                'out_trade_no_2'          => '',
                                'delivery_code'           => '',
                                'order_status_name'       => '',
                                'pay_status'              => '',
                                'remarkk'                 => '',
                                'sku_name'                => '',
                                'goods_chi'               => '',
                                'num'                     => '',
                                'day'                     => '',
                                'goods_num'               => '',
                                'delivery_status'         => '',
                                'refund_status'           => '',
                                'pay_type_name'           => '',
                                'delivery_type_name'      => '',
                                'mobile'                  => '',
                                'telephone'               => '',
                                'full_address'            => '',
                                'buyer_ip'                => '',
                                'buyer_ask_delivery_time' => '',
                                'buyer_message'           => '',
                                'goods_money'             => '',
                                'server_money'             => '',
                                //'delivery_money'          => '',
                                //'promotion_money'         => '',
                                //'coupon_money'            => '',
                                'order_money'             => '',
                                'adjust_money'            => '',
                                'balance_money'           => '',
                                'pay_money'               => '',
                                'refund_money'            => '',
                                'pay_time'                => '',
                                'delivery_time'           => '',
                                'sign_time'               => '',
                                'finish_time'             => '',
                                'remark'                  => '',
                                'delivery_status_name'    => '',
                                'delivery_store_name'     => '',
                                'promotion_type_name'     => ''

                            ];
                            $voo['goods_chi'] = $vvv['goods_chi'];
                            $voo['sku_name'] = $vvv['sku_name'];
                            $voo['num'] = $vvv['num'];
                            $result[]=$voo;
                        }
                    }
                }
                $order['data'] = $result;
            }

            foreach($result as $key => $value) {
                //$rowData = $value;                //获取每列数据，转换处理成需要导出的数据
                $rowData =array_intersect_key(array_replace($columns_key, $value), $columns_key);
                //需要格式转换，否则会乱码
                //mb_convert_variables('GBK', 'UTF-8', $rowData);
                fputcsv($fp, $rowData);
            }            //释放变量的内存
            unset($order_result);            //刷新输出缓冲到浏览器
            unset($result);
            ob_flush();            //必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            flush();
        }
        fclose($fp);
        exit();


    }

    /**
     * 订单导出（已账单为主）
     */
    public function exportOrderrr()
    {
        $order_status   = input("order_status", "");//订单状态
        $order_name     = input("order_name", '');
        $pay_type       = input("pay_type", '');
        $order_from     = input("order_from", '');
        $userlist           = input("userlist", '');
        $start_time     = input("start_time", '');
        $end_time       = input("end_time", '');
        $dstart_time     = input("dstart_time", '');
        $dend_time       = input("dend_time", '');
        $pstart_time     = input("pstart_time", '');
        $pend_time       = input("pend_time", '');
        $order_label    = input("order_label", "");
        $search_text    = input("search", '');
        $promotion_type = input("promotion_type", '');
        $order_type     = input("order_type", '1');

        $condition[] = ["order_type", "=", $order_type];
        $condition[] = ["site_id", "=", $this->site_id];
        $condition[] = ["is_delete", "=", 0];
        $condition[] = ["order_status", "in", [1,3,4,10]];
        //订单状态
        if ($order_status != "") {
            $condition[] = ["order_status", "=", $order_status];
        }
        //订单内容 模糊查询
        if ($order_name != "") {
            $condition[] = ["order_name", 'like', "%$order_name%"];
        }
        //订单来源
        if ($order_from != "") {
            $condition[] = ["order_from", "=", $order_from];
        }
        //机构
        if ($userlist != "") {
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$userlist])){
                $datu       = array_merge($datau[$userlist]['bp'],$datau[$userlist]['doctor']);
                $datu       = array_merge($datu,$datau[$userlist]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $userlist];
                }

            }
        }
        //订单支付
        if ($pay_type != "") {
            $condition[] = ["pay_type", "=", $pay_type];
        }
        //营销类型
        if ($promotion_type != "") {
            if ($promotion_type == 'empty') {
                $condition[] = ["promotion_type", "=", ''];
            } else {
                $condition[] = ["promotion_type", "=", $promotion_type];
            }
        }
        if (!empty($start_time) && !empty($end_time)) {
            $condition[] = ["create_time", "between", [date_to_time($start_time), date_to_time($end_time)]];
        }

        if (!empty($dstart_time) && empty($dend_time)) {
            $condition[] = ["delivery_time", ">=", date_to_time($dstart_time)];
        } elseif (empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ["delivery_time", "<=", date_to_time($dend_time)];
        } elseif (!empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ['delivery_time', 'between', [date_to_time($dstart_time), date_to_time($dend_time)]];
        }

        if (!empty($pstart_time) && empty($pend_time)) {
            $condition[] = ["pay_time", ">=", date_to_time($pstart_time)];
        } elseif (empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ["pay_time", "<=", date_to_time($pend_time)];
        } elseif (!empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ['pay_time', 'between', [date_to_time($pstart_time), date_to_time($pend_time)]];
        }

        if ($order_label == 'member_name'){
            $member = model('member')->getInfo([['username', "like", "%$search_text%"]]);
            if ($member){
                $condition[] = ["member_id", "=", $member['member_id']];
            }
        }elseif ($order_label == 'admin_name'){
            $member = model('user')->getInfo([['username', "like", "%$search_text%"]]);
            if ($member){
                $condition[] = ["admin_uid", "=", $member['uid']];
            }
        }else {
            $condition[] = [$order_label, 'like', "%$search_text%"];
        }

        //医生组获取自己的订单
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $this->uid];
                }

            }else{
                $condition[] = ["admin_uid", "=", $this->uid];
            }
        }
        $order_common_model = new OrderCommonModel();
        $order_export_model = new OrderExport();

        $field = $order_export_model->orderrr_field;
        //接收需要展示的字段

        $ordernum       = $order_common_model->getOrderCount($condition);

        //重写导出方法
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        $columns_title =  [];        //设置好告诉浏览器要下载excel文件的headers
        $columns_key =  [];
        foreach($field as $kk=>$vv){
            $columns_title[]=$vv;
            $columns_key[$kk]='';
        }

        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="'.'订单信息-账单明细'.'.csv"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');        $fp = fopen('php://output', 'a');//打开output流
        //mb_convert_variables('GBK', 'UTF-8', $columns);
        fputcsv($fp, $columns_title);//将数据格式化为CSV格式并写入到output流中
        //获取总数，分页循环处理
        $accessNum = $ordernum['data'];
        $perSize = 1000;
        $pages   = ceil($accessNum / $perSize);
        for($i = 1; $i <= $pages; $i++) {
            $order       = $order_common_model->getOrderPageList($condition,$i,$perSize,'','*',0,'og.sku_id,og.order_id,og.sku_name,og.num,o.goods_chi');
            $order_result = [];
            $result=[];
            if(isset($order['data']['list'])){
                $order['data'] = $order['data']['list'];
            }else{
                $order['data'] = [];
            }
            $sorted = $order_export_model->array_orderby($order['data'], 'bp_name', SORT_DESC,'doctor_name', SORT_DESC,'patient_name', SORT_DESC, 'pay_time', SORT_ASC);
            $order['data'] = $sorted;

            if (!empty($order['data'])) {
                $result = [];
                foreach ($order['data'] as $key=>$vo){
                    foreach($vo['order_goods'] as $kkk=>$vvv){
                        if ($kkk==0){
                            $vo['goods_chi'] = $vvv['goods_chi'];
                            $vo['sku_name'] = $vvv['sku_name'];
                            $vo['num'] = $vvv['num'];
                            $vo['pay_status'] = $vo['pay_status'] == 1 ?'已支付':'未支付';
                            $vo['delivery_status'] = $vo['delivery_status'] == 2 ?'已收货':($vo['delivery_status'] == 1?'已发货':'未发货');
                            $vo['pay_time'] = date('Y-m-d h:i:s',$vo['pay_time']);
                            $vo['delivery_time'] = date('Y-m-d h:i:s',$vo['delivery_time']);
                            $vo['sign_time'] = date('Y-m-d h:i:s',$vo['sign_time']);
                            $vo['invoicerate'] = $vo['server_money']+round($vo['goods_money']*$vo['invoice_rate'],2);
                            $result[]=$vo;
                        }else{
                            $voo = [
                                'bp_name'                 => '',
                                'doctor_name'             => '',
                                'patient_name'            => '',
                                'name'                    => '',
                                'order_no'                => '',
                                'site_name'               => '',
                                'order_name'              => '',
                                'order_from_name'         => '',
                                'order_type_name'         => '',
                                'order_promotion_name'    => '',
                                'out_trade_no'            => '',
                                'out_trade_no_2'          => '',
                                'delivery_code'           => '',
                                'order_status_name'       => '',
                                'pay_status'              => '',
                                'remarkk'                 => '',
                                'sku_name'                => '',
                                'goods_chi'               => '',
                                'num'                     => '',
                                'day'                     => '',
                                'goods_num'               => '',
                                'delivery_status'         => '',
                                'refund_status'           => '',
                                'pay_type_name'           => '',
                                'delivery_type_name'      => '',
                                'mobile'                  => '',
                                'telephone'               => '',
                                'full_address'            => '',
                                'buyer_ip'                => '',
                                'buyer_ask_delivery_time' => '',
                                'buyer_message'           => '',
                                'goods_money'             => '',
                                'server_money'             => '',
                                //'delivery_money'          => '',
                                //'promotion_money'         => '',
                                //'coupon_money'            => '',
                                'order_money'             => '',
                                'adjust_money'            => '',
                                'balance_money'           => '',
                                'pay_money'               => '',
                                'refund_money'            => '',
                                'pay_time'                => '',
                                'delivery_time'           => '',
                                'sign_time'               => '',
                                'finish_time'             => '',
                                'remark'                  => '',
                                'remark_mai'                  => '',
                                'delivery_status_name'    => '',
                                'delivery_store_name'     => '',
                                'invoicerate'             => '',
                                'invoice_rate'            => '',
                                'promotion_type_name'     => ''

                            ];
                            $voo['goods_chi'] = $vvv['goods_chi'];
                            $voo['sku_name'] = $vvv['sku_name'];
                            $voo['num'] = $vvv['num'];
                            $result[]=$voo;
                        }
                    }
                }
                $order['data'] = $result;
            }
            foreach($result as $key => $value) {
                //$rowData = $value;                //获取每列数据，转换处理成需要导出的数据
                $rowData =array_intersect_key(array_replace($columns_key, $value), $columns_key);
                //需要格式转换，否则会乱码
                // mb_convert_variables('GBK', 'UTF-8', $rowData);
                fputcsv($fp, $rowData);
            }            //释放变量的内存
            unset($order_result);            //刷新输出缓冲到浏览器
            unset($result);
            ob_flush();            //必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            flush();
        }
        fclose($fp);
        exit();

    }

    /**
     * 订单导出（已订单商品为主）
     */
    public function exportOrderGoods()
    {
        $order_status   = input("order_status", "");//订单状态
        $order_name     = input("order_name", '');
        $pay_type       = input("pay_type", '');
        $order_from     = input("order_from", '');
        $userlist           = input("userlist", '');
        $start_time     = input("start_time", '');
        $end_time       = input("end_time", '');
        $dstart_time     = input("dstart_time", '');
        $dend_time       = input("dend_time", '');
        $pstart_time     = input("pstart_time", '');
        $pend_time       = input("pend_time", '');
        $order_label    = input("order_label", "");
        $search_text    = input("search", '');
        $promotion_type = input("promotion_type", '');
        $order_type     = input("order_type", '1');

        $condition[] = ["o.order_type", "=", $order_type];
        $condition[] = ["o.site_id", "=", $this->site_id];

        $condition[] = ["o.order_status", "in", [1,3,4,10]];
        //订单状态
        if ($order_status != "") {
            $condition[] = ["o.order_status", "=", $order_status];
        }

        //订单内容 模糊查询
        if ($order_name != "") {
            $condition[] = ["o.order_name", 'like', "%$order_name%"];
        }
        //订单来源
        if ($order_from != "") {
            $condition[] = ["o.order_from", "=", $order_from];
        }
        //机构
        if ($userlist != "") {
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$userlist])){
                $datu       = array_merge($datau[$userlist]['bp'],$datau[$userlist]['doctor']);
                $datu       = array_merge($datu,$datau[$userlist]['patient']);
                if ($datu){
                    $condition[] = ["o.admin_uid", "in", $datu];
                }else{
                    $condition[] = ["o.admin_uid", "=", $userlist];
                }

            }
        }
        //订单支付
        if ($pay_type != "") {
            $condition[] = ["o.pay_type", "=", $pay_type];
        }
        //营销类型
        if ($promotion_type != "") {
            if ($promotion_type == 'empty') {
                $condition[] = ["o.promotion_type", "=", ''];
            } else {
                $condition[] = ["o.promotion_type", "=", $promotion_type];
            }
        }
        if (!empty($start_time) && !empty($end_time)) {
            $condition[] = ["o.create_time", "between", [date_to_time($start_time), date_to_time($end_time)]];
        }

        if (!empty($dstart_time) && empty($dend_time)) {
            $condition[] = ["o.delivery_time", ">=", date_to_time($dstart_time)];
        } elseif (empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ["o.delivery_time", "<=", date_to_time($dend_time)];
        } elseif (!empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ['o.delivery_time', 'between', [date_to_time($dstart_time), date_to_time($dend_time)]];
        }

        if (!empty($pstart_time) && empty($pend_time)) {
            $condition[] = ["o.pay_time", ">=", date_to_time($pstart_time)];
        } elseif (empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ["o.pay_time", "<=", date_to_time($pend_time)];
        } elseif (!empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ['o.pay_time', 'between', [date_to_time($pstart_time), date_to_time($pend_time)]];
        }

        if ($search_text != "") {
            if ($order_label == 'member_name'){
                $member = model('member')->getList([['username', "like", "%$search_text%"]]);
                if ($member){
                    $condition[] = ["o.member_id", "in", array_column($member,'member_id')];
                }
            }elseif ($order_label == 'admin_name'){
                $member = model('user')->getList([['username', "like", "%$search_text%"]]);
                if ($member){
                    $condition[] = ["o.admin_uid", "in", array_column($member,'uid')];
                }
            }else {
                $condition[] = [$order_label, 'like', "%$search_text%"];
            }
        }

        //医生组获取自己的订单
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["o.admin_uid", "in", $datu];
                }else{
                    $condition[] = ["o.admin_uid", "=", $this->uid];
                }

            }else{
                $condition[] = ["o.admin_uid", "=", $this->uid];
            }
        }
        $order_common_model = new OrderCommonModel();
        $order_export_model = new OrderExport();

        $field = array_merge($order_export_model->order_field, $order_export_model->order_goods_field);
        //接收需要展示的字段
        $ordernum       = $order_common_model->getOrderGoodsDetailCount($condition);


        //重写导出方法
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        $columns_title =  [];        //设置好告诉浏览器要下载excel文件的headers
        $columns_key =  [];
        foreach($field as $kk=>$vv){
            $columns_title[]=$vv;
            $columns_key[$kk]='';
        }

        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="'.'订单信息'.'.csv"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');        $fp = fopen('php://output', 'a');//打开output流
        //mb_convert_variables('GBK', 'UTF-8', $columns);
        fputcsv($fp, $columns_title);//将数据格式化为CSV格式并写入到output流中
        //获取总数，分页循环处理
        $accessNum = $ordernum['data'];
        $perSize = 1000;
        $pages   = ceil($accessNum / $perSize);
        for($i = 1; $i <= $pages; $i++) {
            $order       = $order_common_model->getOrderGoodsDetailPageList($condition,$i,$perSize);
            if (!empty($order['data'])) {
                foreach ($order['data']['list'] as $key=>$vo) {

                    //品牌，中文名，库存
                    $goods_info = model('goods')->getInfo([['goods_id', '=', $vo['goods_id']], ['site_id', '=', $this->site_id]], '*');
                    $order['data']['list'][$key]['goods_chi'] = $goods_info['goods_chi'];
                    $order['data']['list'][$key]['goods_stock'] = $goods_info['goods_stock'];
                    $category_name='';
                    /*if ($goods_info['category_id']){
                        $category_name = model('goods_category')->getColumn([['category_id', 'in', $goods_info['category_id']], ['pid', '=', 55]], 'category_name');
                        if (is_array($category_name)) {
                            $category_name = implode('/', $category_name);
                        }
                    }*/

                    $order['data']['list'][$key]['category_name'] = $category_name;

                }

            }
            foreach($order['data']['list'] as $key => $value) {
                //$rowData = $value;                //获取每列数据，转换处理成需要导出的数据
                $rowData =array_intersect_key(array_replace($columns_key, $value), $columns_key);
                //需要格式转换，否则会乱码
                // mb_convert_variables('GBK', 'UTF-8', $rowData);
                fputcsv($fp, $rowData);
            }            //释放变量的内存
             //刷新输出缓冲到浏览器
            unset($order);
            ob_flush();            //必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            flush();
        }
        fclose($fp);
        exit();


    }

    /**
     * 订单导出（已订单商品为主）
     */
    public function exportOrderGoodss()
    {
        $order_status   = input("order_status", "");//订单状态
        $order_name     = input("order_name", '');
        $pay_type       = input("pay_type", '');
        $order_from     = input("order_from", '');
        $userlist           = input("userlist", '');
        $start_time     = input("start_time", '');
        $end_time       = input("end_time", '');
        $dstart_time     = input("dstart_time", '');
        $dend_time       = input("dend_time", '');
        $pstart_time     = input("pstart_time", '');
        $pend_time       = input("pend_time", '');
        $order_label    = input("order_label", "");
        $search_text    = input("search", '');
        $promotion_type = input("promotion_type", '');
        $order_type     = input("order_type", '1');

        $condition[] = ["o.order_type", "=", $order_type];
        $condition[] = ["o.site_id", "=", $this->site_id];

        $condition[] = ["o.order_status", "in", [1,3,4,10]];
        //订单状态
        if ($order_status != "") {
            $condition[] = ["o.order_status", "=", $order_status];
        }

        //订单内容 模糊查询
        if ($order_name != "") {
            $condition[] = ["o.order_name", 'like', "%$order_name%"];
        }
        //订单来源
        if ($order_from != "") {
            $condition[] = ["o.order_from", "=", $order_from];
        }
        //机构
        if ($userlist != "") {
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$userlist])){
                $datu       = array_merge($datau[$userlist]['bp'],$datau[$userlist]['doctor']);
                $datu       = array_merge($datu,$datau[$userlist]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $userlist];
                }

            }
        }
        //订单支付
        if ($pay_type != "") {
            $condition[] = ["o.pay_type", "=", $pay_type];
        }
        //营销类型
        if ($promotion_type != "") {
            if ($promotion_type == 'empty') {
                $condition[] = ["o.promotion_type", "=", ''];
            } else {
                $condition[] = ["o.promotion_type", "=", $promotion_type];
            }
        }
        if (!empty($start_time) && !empty($end_time)) {
            $condition[] = ["o.create_time", "between", [date_to_time($start_time), date_to_time($end_time)]];
        }
        if (!empty($dstart_time) && empty($dend_time)) {
            $condition[] = ["o.delivery_time", ">=", date_to_time($dstart_time)];
        } elseif (empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ["o.delivery_time", "<=", date_to_time($dend_time)];
        } elseif (!empty($dstart_time) && !empty($dend_time)) {
            $condition[] = ['o.delivery_time', 'between', [date_to_time($dstart_time), date_to_time($dend_time)]];
        }
        if (!empty($pstart_time) && empty($pend_time)) {
            $condition[] = ["o.pay_time", ">=", date_to_time($pstart_time)];
        } elseif (empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ["o.pay_time", "<=", date_to_time($pend_time)];
        } elseif (!empty($pstart_time) && !empty($pend_time)) {
            $condition[] = ['o.pay_time', 'between', [date_to_time($pstart_time), date_to_time($pend_time)]];
        }
        if ($search_text != "") {
            if ($order_label == 'member_name'){
                $member = model('member')->getList([['username', "like", "%$search_text%"]]);
                if ($member){
                    $condition[] = ["o.member_id", "in", array_column($member,'member_id')];
                }
            }elseif ($order_label == 'admin_name'){
                $member = model('user')->getList([['username', "like", "%$search_text%"]]);
                if ($member){
                    $condition[] = ["o.admin_uid", "in", array_column($member,'uid')];
                }
            }else {
                $condition[] = [$order_label, 'like', "%$search_text%"];
            }
        }
        //医生组获取自己的订单
        $user = model('user')->getInfo([['uid', "=", $this->uid]]);
        if ($user['group_id'] == 2){
            $user_model = new UserModel();
            $datau      = $user_model->bpdoctordata();
            if (isset($datau[$this->uid])){
                $datu       = array_merge($datau[$this->uid]['bp'],$datau[$this->uid]['doctor']);
                $datu       = array_merge($datu,$datau[$this->uid]['patient']);
                if ($datu){
                    $condition[] = ["admin_uid", "in", $datu];
                }else{
                    $condition[] = ["admin_uid", "=", $this->uid];
                }

            }else{
                $condition[] = ["admin_uid", "=", $this->uid];
            }
        }
        $order_common_model = new OrderCommonModel();
        $order_export_model = new OrderExport();

        $field = $order_export_model->order_goodss_field;
        //接收需要展示的字段

        //重写导出方法
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        $columns_title =  [];        //设置好告诉浏览器要下载excel文件的headers
        $columns_key =  [];
        foreach($field as $kk=>$vv){
            $columns_title[]=$vv;
            $columns_key[$kk]='';
        }

        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="'.'订单信息-商品维度'.'.csv"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');        $fp = fopen('php://output', 'a');//打开output流
        //mb_convert_variables('GBK', 'UTF-8', $columns);
        fputcsv($fp, $columns_title);//将数据格式化为CSV格式并写入到output流中
        //获取总数，分页循环处理
        $order       = $order_common_model->getOrderGoodsDetailGroupList($condition);

        if (!empty($order['data'])) {
            $order_result=[];
            foreach ($order['data'] as $key=>$vo){

                //品牌，中文名，库存
                $goods_info = model('goods')->getInfo([['goods_id', '=', $vo['goods_id']], ['site_id', '=', $this->site_id]], '*');
                if($goods_info){
                    $goods_info['num'] = $vo['num_total'];
                    $category_name='';
                    $category_image='';
                    if ($goods_info['category_id']) {
                        // 获取品牌分类信息（包括名称和图片）
                        $category_info = model('goods_category')->getList([['category_id', 'in', $goods_info['category_id']], ['pid', '=', 55]], 'category_name,image');
                        if (!empty($category_info)) {
                            $category_names = [];
                            $category_images = [];
                            foreach ($category_info as $cat) {
                                $category_names[] = $cat['category_name'];
                                if (!empty($cat['image'])) {
                                    $category_images[] = $cat['image'];
                                }
                            }
                            $category_name = implode('/', $category_names);
                            $category_image = implode(',', $category_images); // 多个图片用逗号分隔
                        }
                    }
                    $goods_info['category_name'] = $category_name;
                    $goods_info['category_image'] = $category_image;
                    $order_result[]=$goods_info;
                }

            }
        }
        foreach($order_result as $key => $value) {
            //$rowData = $value;                //获取每列数据，转换处理成需要导出的数据
            $rowData =array_intersect_key(array_replace($columns_key, $value), $columns_key);
            //需要格式转换，否则会乱码
            // mb_convert_variables('GBK', 'UTF-8', $rowData);
            fputcsv($fp, $rowData);
        }            //释放变量的内存
        unset($order_result);            //刷新输出缓冲到浏览器
        unset($order);
        ob_flush();            //必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
        flush();
        fclose($fp);
        exit();

    }

    /**
     * 订单导出（维权订单）
     */
    public function exportRefundOrder()
    {
        $refund_status   = input("refund_status", "");//退款状态
        $sku_name        = input("sku_name", '');//商品名称
        $refund_type     = input("refund_type", '');//退款方式
        $start_time      = input("start_time", '');//开始时间
        $end_time        = input("end_time", '');//结束时间
        $order_no        = input("order_no", '');//订单编号
        $delivery_status = input("delivery_status", '');//物流状态
        $refund_no       = input("refund_no", '');//退款编号

        $delivery_no        = input("delivery_no", '');//物流编号
        $refund_delivery_no = input("refund_delivery_no", '');//退款物流编号

        $order_common_model = new OrderCommonModel();

        $condition[] = ['og.site_id', '=', $this->site_id];
        //退款状态
        if ($refund_status != "") {
            $condition[] = ["og.refund_status", "=", $refund_status];
        } else {
            $condition[] = ["og.refund_status", "<>", 0];
        }
        //物流状态
        if ($delivery_status != "") {
            $condition[] = ["og.delivery_status", "=", $delivery_status];
        }
        //商品名称
        if ($sku_name != "") {
            $condition[] = ["og.sku_name", "like", "%$sku_name%"];
        }
        //退款方式
        if ($refund_type != "") {
            $condition[] = ["og.refund_type", "=", $refund_type];
        }
        //退款编号
        if ($refund_no != "") {
            $condition[] = ["og.refund_no", "like", "%$refund_no%"];
        }
        //订单编号
        if ($order_no != "") {
            $condition[] = ["og.order_no", "like", "%$order_no%"];
        }
        //物流编号
        if ($delivery_no != "") {
            $condition[] = ["og.delivery_no", "like", "%$delivery_no%"];
        }
        //退款物流编号
        if ($refund_delivery_no != "") {
            $condition[] = ["og.refund_delivery_no", "like", "%$refund_delivery_no%"];
        }

        if (!empty($start_time) && empty($end_time)) {
            $condition[] = ["og.refund_action_time", ">=", date_to_time($start_time)];
        } elseif (empty($start_time) && !empty($end_time)) {
            $condition[] = ["og.refund_action_time", "<=", date_to_time($end_time)];
        } elseif (!empty($start_time) && !empty($end_time)) {
            $condition[] = ['og.refund_action_time', 'between', [date_to_time($start_time), date_to_time($end_time)]];
        }

        $order_export_model = new OrderExport();
        $field              = array_merge($order_export_model->order_field, $order_export_model->order_goods_field);
        //接收需要展示的字段
        $order       = $order_common_model->getOrderGoodsDetailList($condition);

        //重写导出方法
        set_time_limit(0);
        ini_set('memory_limit', '1024M');

        $columns_title =  [];        //设置好告诉浏览器要下载excel文件的headers
        $columns_key =  [];
        foreach($field as $kk=>$vv){
            $columns_title[]=$vv;
            $columns_key[$kk]='';
        }

        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="'.'退款维权订单'.'.csv"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');        $fp = fopen('php://output', 'a');//打开output流
        //mb_convert_variables('GBK', 'UTF-8', $columns);
        fputcsv($fp, $columns_title);//将数据格式化为CSV格式并写入到output流中
        //获取总数，分页循环处理
        $accessNum = count($order['data'])+0;
        $perSize = 1000;
        $pages   = ceil($accessNum / $perSize);
        for($i = 1; $i <= $pages; $i++) {
            $order       = $order_common_model->getOrderGoodsDetailPageList($condition,$i,$perSize);
            $order_result = [];
            if(isset($order['data']['list'])){
                $order['data'] = $order['data']['list'];
            }else{
                $order['data'] = [];
            }
            if (!empty($order['data'])) {
                foreach ($order['data'] as $key=>$vo){
                    $order_result[]=$vo;
                }
            }
            foreach($order_result as $key => $value) {
                //$rowData = $value;                //获取每列数据，转换处理成需要导出的数据
                $rowData =array_intersect_key(array_replace($columns_key, $value), $columns_key);
                //需要格式转换，否则会乱码
                // mb_convert_variables('GBK', 'UTF-8', $rowData);
                fputcsv($fp, $rowData);
            }            //释放变量的内存
            unset($order_result);            //刷新输出缓冲到浏览器
            unset($order);
            ob_flush();            //必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
            flush();
        }
        fclose($fp);
        exit();

    }

    /**
     * 导出字段
     * @return array
     */
    public function getPrintingField()
    {
        $model = new OrderExport();
        $data  = [
            'order_field'       => $model->order_field,
            'order_goods_field' => $model->order_goods_field
        ];

        return success('1', '', $data);
    }

    public function printOrder()
    {
        $order_id            = input('order_id', 0);
        $biaoqiantiaoli      = input('biaoqiantiaoli', '');
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];

        $province = model('area')->getInfo([['id', '=', $order_detail['province_id']]]);
        $city = model('area')->getInfo([['id', '=', $order_detail['city_id']]]);
        $county = model('area')->getInfo([['id', '=', $order_detail['district_id']]]);
        $order_detail['address'] = $province['name'].$city['name'].$county['name'].$order_detail['address'];

        // 处理PDF字段数据，获取运单信息
        $pdf_data = [];
        $qrcode_data = '';
        $waybill_code = '';
        $pro_code = '';
        if (!empty($order_detail['pdf'])) {
            $pdf_json = json_decode($order_detail['pdf'], true);
            if ($pdf_json && isset($pdf_json['list'])) {
                $pdf_data = $pdf_json['list'];
                $waybill_code = isset($pdf_data['waybillcode']) ? $pdf_data['waybillcode'] : '';
                $pro_code = isset($pdf_data['procode']) ? $pdf_data['procode'] : '';
                $qrcode_data = isset($pdf_data['twodimensioncode']) ? $pdf_data['twodimensioncode'] : '';
            }
        }

        // 生成二维码图片
        $qrcode_path = '';
        if (!empty($qrcode_data)) {
            $qrcode_dir = 'upload/qrcode/waybill/';
            if (!is_dir($qrcode_dir)) {
                mkdir($qrcode_dir, 0755, true);
            }
            $qrcode_filename = 'waybill_' . $order_id . '_' . time();
            $qrcode_path = qrcode($qrcode_data, $qrcode_dir, $qrcode_filename);
        }

        // 寄件人固定信息
        $sender_info = [
            'name' => 'Daisy',
            'phone' => '68591828',
            'address' => 'Hongkong53 Kin Wing Street, Tuen Mun'
        ];

        // 生成目的地代码（根据收件地址）
        $dest_code = $this->generateDestCode($order_detail);

        // 处理商品信息，生成寄托物描述
        $goods_info = $this->processGoodsInfo($order_detail);

        $this->assign("pdf_data", $pdf_data);
        $this->assign("waybill_code", $waybill_code);
        $this->assign("pro_code", $pro_code);
        $this->assign("qrcode_path", $qrcode_path);
        $this->assign("sender_info", $sender_info);
        $this->assign("dest_code", $dest_code);
        $this->assign("total_goods_num", $goods_info['total_num']);
        $this->assign("goods_description", $goods_info['description']);
        $this->assign("billing_weight", $goods_info['billing_weight']);
        $this->assign("actual_weight", $goods_info['actual_weight']);

        //获取医生信息
        $user = model('user')->getInfo([['uid','=',$order_detail['admin_uid']]]);
        $member = model('member')->getInfo([['member_id','=',$user['member_id']]]);
        $doctor = $user['username'].'  '.$member['mobile'];

        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctorList();
        if (isset($user_bpdotocr[$order_detail['admin_uid']]['bp'])){
            $bp_name = $user_bpdotocr[$order_detail['admin_uid']]['bp'];
        }else{
            $bp_name = '';
        }
        $this->assign("doctor", $doctor);
        $this->assign("bp_name", $bp_name);

        //获取用户账号
        $buyer = model('member')->getInfo([['member_id','=',$order_detail['member_id']]]);
        if ($buyer){
            $order_detail['name'] =  $buyer['username'];
        }

        // 生成客户姓名与订单编号二维码
        $order_qr_dir = 'upload/qrcode/order';
        $customer_qrcode_path = qrcode((string)($order_detail['name'] ?? ''), $order_qr_dir, 'customer_' . $order_id);
        $order_qrcode_path = qrcode((string)($order_detail['order_no'] ?? $order_id), $order_qr_dir, 'orderno_' . $order_id);
        $this->assign("customer_qrcode_path", $customer_qrcode_path);
        $this->assign("order_qrcode_path", $order_qrcode_path);

        //获取分装产品信息
        $attr_class_info = model('order_attr_fenzhuang')->getInfo([['order_id', '=', $order_detail['out_trade_no']]], '*');
        if (empty($attr_class_info)) {
            return $this->error('未找到分装产品信息');
        }

        $goods_model = new GoodsModel();

        $field = 'g.goods_chi,g.category_id,g.category_json,gs.sku_id,gs.sku_no,gs.sku_name,gs.price,gs.stock,gs.sku_image,gs.goods_id,gs.goods_class_name,g.label_id,gs.shelf_no';

        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];

        // 解析分装产品的 goods_name 和 goods_num
        $goods_name = json_decode($attr_class_info['goods_name'], true);
        $goods_num = json_decode($attr_class_info['goods_num'], true);

        if (empty($goods_name) || empty($goods_num)) {
            return $this->error('分装产品数据格式错误');
        }

        // 提取所有 sku_id
        $sku_ids = [];
        foreach ($goods_name as $item) {
            $parts = explode('_', $item);
            if (count($parts) >= 2) {
                $sku_ids[] = intval($parts[1]);
            }
        }

        if (empty($sku_ids)) {
            return $this->error('未找到有效的产品SKU');
        }

        $goods_sku_list = $goods_model->getGoodsSkuList([['gs.sku_id', 'in', $sku_ids], ['gs.site_id', '=', $this->site_id]], $field, 'gs.price asc',null,$alias,$join);
        $goods_sku = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            //获取分类
            $category_name='';
            $category_image='';
            if ($vo['category_id']) {
                // 获取品牌分类信息（包括名称和图片）
                $category_info = model('goods_category')->getList([['category_id', 'in', $vo['category_id']], ['pid', '=', 55]], 'category_name,image');
                if (!empty($category_info)) {
                    $category_names = [];
                    $category_images = [];
                    foreach ($category_info as $cat) {
                        $category_names[] = $cat['category_name'];
                        if (!empty($cat['image'])) {
                            $category_images[] = $cat['image'];
                        }
                    }
                    $category_name = implode('/', $category_names);
                    $category_image = implode(',', $category_images); // 多个图片用逗号分隔
                }
            }
            $vo['category_name'] = $category_name;
            $vo['category_image'] = $category_image;
            //如果产品是固体或者液体，而且用户自定义不展示产品图片则替换图片
            if ($vo['label_id'] == 1 && $attr_class_info['fenji']==1 && $attr_class_info['c_fenji']==1){
                $vo['sku_image'] = 'upload/1/common/images/20241224/20241224083613173504377375730.png';
            }
            $goods_sku[$vo['sku_id']] = $vo;
        }
        // 时间段映射
        $time_mapping = [
            'earlymoning' => '晨起包',
            'moning' => '早餐包',
            'aftnoon' => '午餐包',
            'canjian' => '餐间包',
            'night' => '晚餐包',
            'evening' => '睡前包'
        ];

        // 按时间段分组产品
        $time_groups = [];
        $total_time_periods = 0;

        foreach ($goods_name as $key => $item) {
            $parts = explode('_', $item);
            if (count($parts) >= 2) {
                $time_period = $parts[0];
                $sku_id = intval($parts[1]);
                $quantity = intval($goods_num[$key]);

                if ($quantity > 0 && isset($goods_sku[$sku_id])) {
                    if (!isset($time_groups[$time_period])) {
                        $time_groups[$time_period] = [];
                        $total_time_periods++;
                    }

                    $product = $goods_sku[$sku_id];
                    $product['num'] = $quantity;
                    $time_groups[$time_period][] = $product;
                }
            }
        }

        // 计算各种包装材料数量
        $packaging_counts = [
            'meal_box' => $total_time_periods,  // 餐盒数量 = 时间段数量
            'envelope' => $total_time_periods,  // 封套数量 = 时间段数量
            'seal_sticker' => $total_time_periods, // 封口贴数量 = 时间段数量
            'brand_manual' => 1,  // 品牌手册固定为1
            'personal_manual' => 1, // 个性化说明书固定为1
            'express_bill' => 1,  // 快递单固定为1
            'outer_box_small' => 0, // 外盒小
            'outer_box_large' => 0  // 外盒大
        ];

        // 外盒规则：1-2个时间段用小盒，超过2个时间段用大盒
        if ($total_time_periods <= 2) {
            $packaging_counts['outer_box_small'] = 1;
        } else {
            $packaging_counts['outer_box_large'] = 1;
        }

        // 统计各时间段的服药粒数与总数
        $period_pill_counts = [];
        foreach ($time_groups as $periodKey => $productsInPeriod) {
            $sumForPeriod = 0;
            foreach ($productsInPeriod as $productRow) {
                $sumForPeriod += intval($productRow['num'] ?? 0);
            }
            $period_pill_counts[$periodKey] = $sumForPeriod;
        }
        $total_pills = array_sum($period_pill_counts);

        // 传递数据到模板
        $this->assign("time_groups", $time_groups);
        $this->assign("time_mapping", $time_mapping);
        $this->assign("packaging_counts", $packaging_counts);
        $this->assign("total_time_periods", $total_time_periods);
        $this->assign("period_pill_counts", $period_pill_counts);
        $this->assign("total_pills", $total_pills);
        if ($biaoqiantiaoli) {
            $attr_class_info['day'] = $biaoqiantiaoli;
        }
        if (request()->isAjax()) {
            $result = [];
            $result['remark_extra'] = $attr_class_info['day'];
            return success(0, 'SUCCESS',$result);
            exit;
        }
        $this->assign("attr_class_info", $attr_class_info);
        $this->assign("order_detail", $order_detail);
        //查验 粉剂
        $order_detail['remarkfenkou'] = $order_detail['remarkfenji'] = '';
        if ($attr_class_info['ccheck']==1){
            $order_detail['remarkfenkou'] = '不封口;';
        }else{
            $order_detail['remarkfenkou'] = '封口;';
        }

        if ($attr_class_info['fenji']==1){
            if ($attr_class_info['c_fenji']==1){
                $order_detail['remarkfenji'] =  '分装';
            }else{
                $order_detail['remarkfenji'] =  '不分装';
            }
        }
        $this->assign("order_detail", $order_detail);
        return $this->fetch('order/print_order');
    }

    /**
     * 生成目的地代码
     * @param array $order_detail
     * @return string
     */
    private function generateDestCode($order_detail)
    {
        $dest_code = '851SC-006';

        // 可根据省市信息生成不同的目的地代码
        if (!empty($order_detail['province_id'])) {
            // TODO: 根据省市ID映射到不同的目的地代码
        }

        return $dest_code;
    }

    /**
     * 处理商品信息
     * @param array $order_detail
     * @return array
     */
    private function processGoodsInfo($order_detail)
    {
        $total_num = 0;
        $description = '';
        $billing_weight = '';
        $actual_weight = '';

        if (!empty($order_detail['order_goods'])) {
            $goods_names = [];
            foreach ($order_detail['order_goods'] as $goods) {
                $total_num += $goods['num'];
                $goods_name = !empty($goods['goods_chi']) ? $goods['goods_chi'] : $goods['sku_name'];
                $goods_names[] = $goods_name . '*' . $goods['num'];
            }
            $description = implode(', ', $goods_names);

            if (mb_strlen($description) > 100) {
                $description = mb_substr($description, 0, 100) . '...';
            }
        } else {
            $total_num = $order_detail['goods_num'] ?? 9;
            $description = 'FATMAGIC脂魔力膳食营养补充剂*' . $total_num;
        }

        return [
            'total_num' => $total_num,
            'description' => $description,
            'billing_weight' => $billing_weight,
            'actual_weight' => $actual_weight
        ];
    }

    public function printOrderr()
    {
        $order_id            = input('order_id', 0);
        $biaoqiantiaoli      = input('biaoqiantiaoli', '');
        $type            = input('type', 0);
        //下单时候查看既往订单
        $api            = input('api', 0);
        //判断是否是未来医生账号
        $weilai   = input("weilaiyisheng", '0');
        $sys_user = model('user')->getInfo([['uid','=',$this->uid]]);
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];

        //获取医生信息
        $user = model('user')->getInfo([['uid','=',$order_detail['admin_uid']]]);
        $member = model('member')->getInfo([['member_id','=',$user['member_id']]]);
        $doctor = $user['username'].'  '.$member['mobile'];
        $this->assign("doctor", $doctor);

        //获取机构logo
        $user_model = new UserModel();
        $user_bpdotocr = $user_model->bpdoctoruidList();
        if (isset($user_bpdotocr[$order_detail['admin_uid']]['bp'])){
            $logouid = $user_bpdotocr[$order_detail['admin_uid']]['bp'];
        }else{
            if (isset($user_bpdotocr[$order_detail['admin_uid']]['doctor'])){
                $logouid = $user_bpdotocr[$order_detail['admin_uid']]['doctor'];
            }else{
                $logouid = '';
            }
        }
        if ($logouid){
            $logo = model('user')->getInfo([['uid','=',$logouid]]);
            $logo = $logo['headimg'];
        }else{
            $logo = '';
        }
        $this->assign("logo", $logo);

        //获取用户账号
        $buyer = model('member')->getInfo([['member_id','=',$order_detail['member_id']]]);
        if ($buyer){
            $order_detail['name'] =  $buyer['username'];
        }
        //商品类型信息
        $attr_class_info = model('order_attr_fenzhuang')->getInfo([['order_id', '=', $order_detail['out_trade_no']], ['site_id', '=', $this->site_id]], '*');
        $goods_model = new GoodsModel();

        $field = 'g.introduction,g.introd,g.goods_chi,g.category_id,g.category_json,gs.sku_id,gs.sku_name,gs.price,gs.stock,gs.sku_image,gs.goods_id,gs.goods_class_name,g.label_id';

        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];
        $goods_sku_list                             = $goods_model->getGoodsSkuList([['gs.sku_id', 'in', $attr_class_info['sku_ids']], ['gs.site_id', '=', $this->site_id]], $field, 'gs.price asc',null,$alias,$join);
        $goods_sku = [];
        $goods_category = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            //获取分类
            $category_name='';
            $category_image='';
            if ($vo['category_id']) {
                // 获取品牌分类信息（包括名称和图片）
                $category_info = model('goods_category')->getList([['category_id', 'in', $vo['category_id']], ['pid', '=', 55]], 'category_name,image');
                if (!empty($category_info)) {
                    $category_names = [];
                    $category_images = [];
                    foreach ($category_info as $cat) {
                        $category_names[] = $cat['category_name'];
                        if (!empty($cat['image'])) {
                            $category_images[] = $cat['image'];
                        }
                    }
                    $category_name = implode('/', $category_names);
                    $category_image = implode(',', $category_images); // 多个图片用逗号分隔
                }
            }
            $vo['category_name'] = $category_name;
            $vo['category_image'] = $category_image;
            //如果产品是固体或者液体，而且用户自定义不展示产品图片则替换图片
            if ($vo['label_id'] == 1 && $attr_class_info['fenji']==1 && $attr_class_info['c_fenji']==1){
                $vo['sku_image'] = 'upload/1/common/images/20241224/20241224083613173504377375730.png';
            }
            $goods_sku[$vo['sku_id']] = $vo;
        }
        $goods_category = array_unique($goods_category);
        $goods_category = array_splice($goods_category,0,4);
        $goods_name = json_decode($attr_class_info['goods_name'],true);
        $goods_num  = json_decode($attr_class_info['goods_num'],true);
        $goods_list = [];
        foreach ($goods_name as $key=>$vo){
            $goods = explode('_',$vo);
            $goods_detail = $goods_sku[$goods[1]];
            //过滤掉数值为0的
            if ($goods_num[$key]==0){
                continue;
            }
            $goods_detail['num'] = $goods_num[$key];
            $goods_list[$goods[0]][$goods[1]]=$goods_detail;
        }

        $time = ($attr_class_info['earlymoning_time']?'晨起后':'').' '. ($attr_class_info['moning_time']?'早餐后':'').' '. ($attr_class_info['aftnoon_time']?'午餐后':'').' '. ($attr_class_info['canjian_time']?'餐间':'').' '.
            ($attr_class_info['night_time']?'晚餐后':'').' '. ($attr_class_info['sleep_time']?'晚睡前':'');
        $attr_class_info['earlymoning_time'] = substr($attr_class_info['earlymoning_time'],0,18);
        $attr_class_info['moning_time'] = substr($attr_class_info['moning_time'],0,18);
        $attr_class_info['aftnoon_time'] = substr($attr_class_info['aftnoon_time'],0,18);
        $attr_class_info['canjian_time'] = substr($attr_class_info['canjian_time'],0,18);
        $attr_class_info['night_time'] = substr($attr_class_info['night_time'],0,18);
        $attr_class_info['sleep_time'] = substr($attr_class_info['sleep_time'],0,18);
        //重新整理打印材料
        $earlymoning = isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[];
        $moning = isset($goods_list['moning'])?$goods_list['moning']:[];
        $aftnoon = isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[];
        $canjian = isset($goods_list['canjian'])?$goods_list['canjian']:[];
        $night = isset($goods_list['night'])?$goods_list['night']:[];
        $evening = isset($goods_list['evening'])?$goods_list['evening']:[];

        foreach ($earlymoning as $key=>$vo){
            $earlymoning[$key]['color'] = '#aacbeb';
            $earlymoning[$key]['type'] = 1;
        }
        foreach ($moning as $key=>$vo){
            $moning[$key]['color'] = '#c6b5e5';
            $moning[$key]['type'] = 2;
        }
        foreach ($aftnoon as $key=>$vo){
            $aftnoon[$key]['color'] = '#e1bda7';
            $aftnoon[$key]['type'] = 3;
        }
        foreach ($canjian as $key=>$vo){
            $canjian[$key]['color'] = '#ffea64';
            $canjian[$key]['type'] = 4;
        }
        foreach ($night as $key=>$vo){
            $night[$key]['color'] = '#96d4e1';
            $night[$key]['type'] = 5;
        }
        foreach ($evening as $key=>$vo){
            $evening[$key]['color'] = '#f7d1d8';
            $evening[$key]['type'] = 6;
        }
        $all = array_merge($earlymoning,$moning,$aftnoon,$canjian,$night,$evening);
        $pages = [];
        if ($logo){
            foreach ($all as $key=>$vo){
                if ($key<6){
                    $pages[0][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>5&&$key<14){
                    $pages[1][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>13&&$key<22){
                    $pages[2][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>21&&$key<30){
                    $pages[3][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>29&&$key<38){
                    $pages[4][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>37&&$key<46){
                    $pages[5][$vo['type']][] = $vo;
                    continue;
                }

            }
        }else{
            foreach ($all as $key=>$vo){
                if ($key<7){
                    $pages[0][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>6&&$key<18){
                    $pages[1][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>17&&$key<29){
                    $pages[2][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>28&&$key<40){
                    $pages[3][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>39&&$key<51){
                    $pages[4][$vo['type']][] = $vo;
                    continue;
                }
                if ($key>50&&$key<62){
                    $pages[5][$vo['type']][] = $vo;
                    continue;
                }

            }
        }

        $this->assign("pages",$pages);
        $this->assign("earlymoning", isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[]);
        $this->assign("moning", isset($goods_list['moning'])?$goods_list['moning']:[]);
        $this->assign("aftnoon", isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[]);
        $this->assign("canjian", isset($goods_list['canjian'])?$goods_list['canjian']:[]);
        $this->assign("night", isset($goods_list['night'])?$goods_list['night']:[]);
        $this->assign("evening", isset($goods_list['evening'])?$goods_list['evening']:[]);
        if ($biaoqiantiaoli) {
            $attr_class_info['day'] = $biaoqiantiaoli;
        }
        if (request()->isAjax()) {
            $result = [];
            $result['remark_extra'] = $attr_class_info['day'];
            return success(0, 'SUCCESS',$result);
            exit;
        }
        $this->assign("attr_class_info", $attr_class_info);
        $this->assign("order_detail", $order_detail);
        $this->assign("time",$time);
        $this->assign("goods_category",implode(' ',$goods_category));

        $this->assign("order_detail", $order_detail);

        $visit_type = [
            1=>[1,1,1,1,1,1],
            2=>[1,1,1,1,1,0],
            3=>[1,1,1,1,0,1],
            4=>[1,1,1,0,1,1],
            5=>[1,1,1,0,0,0],
            6=>[1,1,1,1,1,1]
        ];
        $visit = isset($visit_type[$sys_user['gexinghua']])?$visit_type[$sys_user['gexinghua']]:[1,1,1,1,1,1];
        $this->assign("visit",$visit);
        if ($weilai == 1){
            return $this->fetch('order/print_orderr_w');
        }
        if ($type==1){
            //下单时候查看既往订单
            if ($api==1){
                return $this->fetch('order/print_orderr_a');
            }else{
                return $this->fetch('order/print_orderr_c');
            }
        }else{
            return $this->fetch('order/print_orderr');
        }

    }

    public function printOrderall()
    {
        $order_id            = input('order_id', 0);
        $type            = input('type', 0);
        //下单时候查看既往订单
        $api            = input('api', 0);
        //判断是否是未来医生账号
        $weilai   = input("weilaiyisheng", '0');
        $sys_user = model('user')->getInfo([['uid','=',$this->uid]]);
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];

        //获取医生信息
        $user = model('user')->getInfo([['uid','=',$order_detail['admin_uid']]]);
        $member = model('member')->getInfo([['member_id','=',$user['member_id']]]);
        $doctor = $user['username'].'  '.$member['mobile'];
        $this->assign("doctor", $doctor);

        //获取用户账号
        $buyer = model('member')->getInfo([['member_id','=',$order_detail['member_id']]]);
        if ($buyer){
            $order_detail['name'] =  $buyer['username'];
        }
        //商品类型信息
        $attr_class_info = model('order_attr')->getInfo([['order_id', '=', $order_detail['out_trade_no']], ['site_id', '=', $this->site_id]], '*');
        $goods_model = new GoodsModel();

        $field = 'g.introduction,g.introd,g.goods_chi,g.category_id,g.category_json,gs.sku_id,gs.sku_name,gs.price,gs.stock,gs.sku_image,gs.goods_id,gs.goods_class_name,g.label_id';

        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];
        $goods_sku_list                             = $goods_model->getGoodsSkuList([['gs.is_delete', '=', 0], ['gs.site_id', '=', $this->site_id]], $field, 'gs.price asc',null,$alias,$join);
        $goods_sku = [];
        $goods_category = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            //获取分类
            $category_name='';
            $category_image='';
            if ($vo['category_id']) {
                // 获取品牌分类信息（包括名称和图片）
                $category_info = model('goods_category')->getList([['category_id', 'in', $vo['category_id']], ['pid', '=', 55]], 'category_name,image');
                if (!empty($category_info)) {
                    $category_names = [];
                    $category_images = [];
                    foreach ($category_info as $cat) {
                        $category_names[] = $cat['category_name'];
                        if (!empty($cat['image'])) {
                            $category_images[] = $cat['image'];
                        }
                    }
                    $category_name = implode('/', $category_names);
                    $category_image = implode(',', $category_images); // 多个图片用逗号分隔
                }
            }
            $vo['category_name'] = $category_name;
            $vo['category_image'] = $category_image;
            //如果产品是固体或者液体，而且用户自定义不展示产品图片则替换图片
            if ($vo['label_id'] == 1 && $sys_user['gexinghua']==6){
                $vo['sku_image'] = 'upload/1/common/images/20241224/20241224083613173504377375730.png';
            }
            $goods_sku[$vo['sku_id']] = $vo;
        }
        $goods_list = [];
        foreach ($goods_sku as $key=>$vo){
            $vo['num'] = 1;
            $goods_list['earlymoring'][$vo['sku_id']]=$vo;
        }

        $time = ($attr_class_info['earlymoning_time']?'晨起后':'').' '. ($attr_class_info['moning_time']?'早餐后':'').' '. ($attr_class_info['aftnoon_time']?'午餐后':'').' '. ($attr_class_info['canjian_time']?'餐间':'').' '.
            ($attr_class_info['night_time']?'晚餐后':'').' '. ($attr_class_info['sleep_time']?'晚睡前':'');
        $attr_class_info['earlymoning_time'] = substr($attr_class_info['earlymoning_time'],0,18);
        $attr_class_info['moning_time'] = substr($attr_class_info['moning_time'],0,18);
        $attr_class_info['aftnoon_time'] = substr($attr_class_info['aftnoon_time'],0,18);
        $attr_class_info['canjian_time'] = substr($attr_class_info['canjian_time'],0,18);
        $attr_class_info['night_time'] = substr($attr_class_info['night_time'],0,18);
        $attr_class_info['sleep_time'] = substr($attr_class_info['sleep_time'],0,18);
        //重新整理打印材料
        $earlymoning = isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[];
        $moning = isset($goods_list['moning'])?$goods_list['moning']:[];
        $aftnoon = isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[];
        $canjian = isset($goods_list['canjian'])?$goods_list['canjian']:[];
        $night = isset($goods_list['night'])?$goods_list['night']:[];
        $evening = isset($goods_list['evening'])?$goods_list['evening']:[];

        foreach ($earlymoning as $key=>$vo){
            $earlymoning[$key]['color'] = '#aacbeb';
            $earlymoning[$key]['type'] = 1;
        }
        foreach ($moning as $key=>$vo){
            $moning[$key]['color'] = '#c6b5e5';
            $moning[$key]['type'] = 2;
        }
        foreach ($aftnoon as $key=>$vo){
            $aftnoon[$key]['color'] = '#e1bda7';
            $aftnoon[$key]['type'] = 3;
        }
        foreach ($canjian as $key=>$vo){
            $canjian[$key]['color'] = '#ffea64';
            $canjian[$key]['type'] = 4;
        }
        foreach ($night as $key=>$vo){
            $night[$key]['color'] = '#96d4e1';
            $night[$key]['type'] = 5;
        }
        foreach ($evening as $key=>$vo){
            $evening[$key]['color'] = '#f7d1d8';
            $evening[$key]['type'] = 6;
        }
        $all = array_merge($earlymoning,$moning,$aftnoon,$canjian,$night,$evening);
        $pages = [];
        foreach ($all as $key=>$vo){
            if ($key<7){
                $pages[0][$vo['type']][] = $vo;
                continue;
            }
            if ($key>6&&$key<18){
                $pages[1][$vo['type']][] = $vo;
                continue;
            }
            if ($key>17&&$key<29){
                $pages[2][$vo['type']][] = $vo;
                continue;
            }
            if ($key>28&&$key<40){
                $pages[3][$vo['type']][] = $vo;
                continue;
            }
            if ($key>39&&$key<51){
                $pages[4][$vo['type']][] = $vo;
                continue;
            }
            if ($key>50&&$key<62){
                $pages[5][$vo['type']][] = $vo;
                continue;
            }

        }
        $this->assign("pages",$pages);
        $this->assign("earlymoning", isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[]);
        $this->assign("moning", isset($goods_list['moning'])?$goods_list['moning']:[]);
        $this->assign("aftnoon", isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[]);
        $this->assign("canjian", isset($goods_list['canjian'])?$goods_list['canjian']:[]);
        $this->assign("night", isset($goods_list['night'])?$goods_list['night']:[]);
        $this->assign("evening", isset($goods_list['evening'])?$goods_list['evening']:[]);
        $this->assign("attr_class_info", $attr_class_info);
        $this->assign("order_detail", $order_detail);
        $this->assign("time",$time);
        $this->assign("goods_category",implode(' ',$goods_category));

        $this->assign("order_detail", $order_detail);

        $visit = [1,1,1,0,1,1];
        $this->assign("visit",$visit);
        return $this->fetch('order/print_orderrall');

    }

    public function printOrdergoods()
    {
        $order_id            = input('order_id', 0);
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];

        //商品类型信息
        $attr_class_info = model('order_attr')->getInfo([['order_id', '=', $order_detail['out_trade_no']], ['site_id', '=', $this->site_id]], '*');
        $goods_model = new GoodsModel();

        $field = 'g.category_json,gs.sku_id,gs.sku_name,gs.price,gs.stock,gs.sku_image,gs.goods_id,gs.goods_class_name,g.goods_imagc';

        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];
        $goods_sku_list                             = $goods_model->getGoodsSkuList([['gs.sku_id', 'in', $attr_class_info['sku_ids']], ['gs.site_id', '=', $this->site_id]], $field, 'gs.price asc',null,$alias,$join);
        $content='';
        foreach ($goods_sku_list['data'] as $key=>$vo){
            if ($vo['goods_imagc']) {
                $goods_imagc = explode(',', $vo['goods_imagc']);
                foreach ($goods_imagc as $k => $v) {
                    if ($v) {
                        $content = $content . '<img src="/' . $v . '"><br>';
                    }
                }
            } else{
              return $this->error($vo['sku_name'].'产品图不能为空');
            }
        }

        $this->assign("content", $content);
        return $this->fetch('order/print_ordergoods');

    }

    public function printGoods()
    {
        $order_id            = input('order_id', 0);
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];

        //获取医生信息
        $user = model('user')->getInfo([['uid','=',$order_detail['admin_uid']]]);
        $member = model('member')->getInfo([['member_id','=',$user['member_id']]]);
        $doctor = $user['username'].'  '.$member['mobile'];
        $this->assign("doctor", $doctor);
        //获取用户账号
        $buyer = model('member')->getInfo([['member_id','=',$order_detail['member_id']]]);
        if ($buyer){
            $order_detail['name'] =  $buyer['username'];
        }


        //商品类型信息
        $attr_class_info = model('order_attr')->getInfo([['order_id', '=', $order_detail['out_trade_no']], ['site_id', '=', $this->site_id]], '*');
        $goods_model = new GoodsModel();

        $field = 'g.goods_chi,g.category_json,gs.sku_id,gs.sku_name,gs.price,gs.stock,gs.sku_image,gs.goods_id,gs.goods_class_name';

        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];
        $goods_sku_list                             = $goods_model->getGoodsSkuList([['gs.sku_id', 'in', $attr_class_info['sku_ids']], ['gs.site_id', '=', $this->site_id]], $field, 'gs.price asc',null,$alias,$join);
        $goods_sku = [];
        $goods_category = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            $goods_sku[$vo['sku_id']] = $vo;
            //获取分类
            $category_json  = json_decode($vo['category_json']);
            foreach ($category_json as $k => $v) {
                if (!empty($v)) {
                    $category_name      = model('goods_category')->getColumn([['category_id', 'in', $v]], 'category_name');
                    $category_name      = implode('/', $category_name);
                    $goods_category[] = $category_name;
                }
            }
        }
        $goods_category = array_unique($goods_category);
        $goods_category = array_splice($goods_category,0,4);
        $goods_name = json_decode($attr_class_info['goods_name'],true);
        $goods_num  = json_decode($attr_class_info['goods_num'],true);
        $goods_list = [];
        foreach ($goods_name as $key=>$vo){
            $goods = explode('_',$vo);
            $goods_detail = $goods_sku[$goods[1]];
            $goods_detail['num'] = $goods_num[$key];
            $goods_list[$goods[0]][$goods[1]]=$goods_detail;
        }

        $time = ($attr_class_info['earlymoning_time']?'晨起后':'').' '. ($attr_class_info['moning_time']?'早餐后':'').' '. ($attr_class_info['aftnoon_time']?'午餐后':'').' '.($attr_class_info['canjian_time']?'餐间':'').' '.
            ($attr_class_info['night_time']?'晚餐后':'').' '. ($attr_class_info['sleep_time']?'晚睡前':'');
        $attr_class_info['earlymoning_time'] = substr($attr_class_info['earlymoning_time'],0,18);
        $attr_class_info['moning_time'] = substr($attr_class_info['moning_time'],0,18);
        $attr_class_info['aftnoon_time'] = substr($attr_class_info['aftnoon_time'],0,18);
        $attr_class_info['canjian_time'] = substr($attr_class_info['canjian_time'],0,18);
        $attr_class_info['night_time'] = substr($attr_class_info['night_time'],0,18);
        $attr_class_info['sleep_time'] = substr($attr_class_info['sleep_time'],0,18);
        $this->assign("earlymoning", isset($goods_list['earlymoring'])?$goods_list['earlymoring']:[]);
        $this->assign("moning", isset($goods_list['moning'])?$goods_list['moning']:[]);
        $this->assign("aftnoon", isset($goods_list['aftnoon'])?$goods_list['aftnoon']:[]);
        $this->assign("canjian", isset($goods_list['canjian'])?$goods_list['canjian']:[]);
        $this->assign("night", isset($goods_list['night'])?$goods_list['night']:[]);
        $this->assign("evening", isset($goods_list['evening'])?$goods_list['evening']:[]);
        $this->assign("attr_class_info", $attr_class_info);
        $this->assign("order_detail", $order_detail);
        $this->assign("time",$time);
        $this->assign("goods_category",implode(' ',$goods_category));
        return $this->fetch('order/print_goods');
    }

    public function printGood()
    {
        $order_id            = input('order_id', 0);
        $biaoqiandate        = input('biaoqiandate', '');
        $biaoqiantiaoli      = input('biaoqiantiaoli', '');
        $kehu                = input('kehu', '');
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];

        //获取医生信息
        $user = model('user')->getInfo([['uid','=',$order_detail['admin_uid']]]);
        $member = model('member')->getInfo([['member_id','=',$user['member_id']]]);
        $doctor = $user['username'].'  '.$member['mobile'];
        $this->assign("doctor", $doctor);
        //获取用户账号
        $buyer = model('member')->getInfo([['member_id','=',$order_detail['member_id']]]);
        if ($buyer){
            $order_detail['name'] =  $buyer['username'];
        }

        // 使用分装表获取商品类型信息
        $attr_class_info = model('order_attr_fenzhuang')->getInfo([['order_id', '=', $order_detail['out_trade_no']]], '*');
        if (!$attr_class_info) {
            // 如果分装表没有数据，回退到原表
            $attr_class_info = model('order_attr')->getInfo([['order_id', '=', $order_detail['out_trade_no']], ['site_id', '=', $this->site_id]], '*');
        }
        
        $goods_model = new GoodsModel();
        $field = 'g.goods_chi,g.category_json,gs.sku_id,gs.sku_name,gs.price,gs.stock,gs.sku_image,gs.goods_id,gs.goods_class_name';
        $alias = 'gs';
        $join  = [
            ['goods g', 'gs.sku_id = g.sku_id', 'inner']
        ];
        $goods_sku_list = $goods_model->getGoodsSkuList([['gs.sku_id', 'in', $attr_class_info['sku_ids']], ['gs.site_id', '=', $this->site_id]], $field, 'gs.price asc',null,$alias,$join);
        
        // 构建商品SKU映射
        $goods_sku = [];
        $goods_category = [];
        foreach ($goods_sku_list['data'] as $key=>$vo){
            $goods_sku[$vo['sku_id']] = $vo;
            //获取分类
            $category_json  = json_decode($vo['category_json']);
            foreach ($category_json as $k => $v) {
                if (!empty($v)) {
                    $category_name = model('goods_category')->getColumn([['category_id', 'in', $v]], 'category_name');
                    $category_name = implode('/', $category_name);
                    $goods_category[] = $category_name;
                }
            }
        }
        $goods_category = array_unique($goods_category);
        $goods_category = array_splice($goods_category,0,4);
        
        // 解析商品数据
        $goods_name = json_decode($attr_class_info['goods_name'], true);
        $goods_num  = json_decode($attr_class_info['goods_num'], true);
        
        // 时间段映射
        $time_mapping = [
            'earlymoning' => '晨起包',
            'moning' => '早餐包',
            'aftnoon' => '午餐包',
            'canjian' => '餐间包',
            'night' => '晚餐包',
            'sleep' => '睡前包'
        ];
        
        // 重构药品安排数据
        $arrange_data = [];
        if ($goods_name && $goods_num) {
            foreach ($goods_name as $key => $vo) {
                $goods = explode('_', $vo);
                if (count($goods) >= 2) {
                    $time_period = $goods[0];
                    $sku_id = $goods[1];
                    $goods_detail = isset($goods_sku[$sku_id]) ? $goods_sku[$sku_id] : null;
                    
                    if ($goods_detail) {
                        $goods_detail['num'] = isset($goods_num[$key]) ? $goods_num[$key] : 1;
                        
                        if (!isset($arrange_data[$time_period])) {
                            $arrange_data[$time_period] = [
                                'name' => isset($time_mapping[$time_period]) ? $time_mapping[$time_period] : $time_period,
                                'goods' => [],
                                'time_field' => $time_period . '_time',
                                'service_time' => ''
                            ];
                        }
                        $arrange_data[$time_period]['goods'][] = $goods_detail;
                    }
                }
            }
        }
        
        // 处理服用时间
        foreach ($arrange_data as $time_period => &$data) {
            $time_field = $data['time_field'];
            if (isset($attr_class_info[$time_field]) && !empty($attr_class_info[$time_field])) {
                $data['service_time'] = $attr_class_info[$time_field];
            } else {
                $data['service_time'] = '随餐服用';
            }
        }
        
        //自定义时间
        if ($biaoqiandate) {
            $order_detail['create_time'] = strtotime($biaoqiandate);
        }
        if ($biaoqiantiaoli) {
            $attr_class_info['day'] = $biaoqiantiaoli;
        }
        if ($kehu) {
            $order_detail['name'] = $kehu;
        }
        
        if (request()->isAjax()) {
            $result = [];
            $result['remark_extra'] = $attr_class_info['day'];
            $result['create_time'] = date('Y-m-d',$order_detail['create_time']);
            $result['kehu'] = $order_detail['name'];
            return success(0, 'SUCCESS',$result);
            exit;
        }
        
        // 传递数据到模板
        $this->assign("attr_class_info", $attr_class_info);
        $this->assign("order_detail", $order_detail);
        $this->assign("arrange_data", $arrange_data);
        $this->assign("timee", date('Ymd'));
        $this->assign("production_date", date('Y年m月d日')); // 生产日期
        $this->assign("goods_category", implode(' ', $goods_category));
        return $this->fetch('order/print_good');
    }

    public function printShunfeng()
    {
        $order_id            = input('order_id', 0);
        $order_common_model  = new OrderCommonModel();
        $order_detail_result = $order_common_model->getOrderDetail($order_id);
        $order_detail        = $order_detail_result["data"];






        //顺丰下单
        if(!$order_detail['pdf'] && $order_detail['pay_status']==1) {
            if ($order_detail['delivery_no']){
                //测试顺丰下单
                $serviceCode = "COM_RECE_CLOUD_PRINT_WAYBILLS";
                $msgData = '{
    "templateCode": "fm_150_standard_QZKJPjIXU2Q",
    "version":"2.0",
    "fileType":"pdf",
    "documents": [{
    "masterWaybillNo": "' . $order_detail['delivery_no'] . '"
    }]
}';
                $order_common_model->shunfeng($serviceCode, $msgData);
                $this->error('请刷新页面重新生成运单');


            }
            $shunfeng_g[] = [
                "amount" => 100,
                "count" => 1,
                "name" => '保健品',
                "unit" => "瓶",
                "weight" => '20'
            ];
            //收货地址
            $province = model('area')->getInfo([['id', '=', $order_detail['province_id']]]);
            $city = model('area')->getInfo([['id', '=', $order_detail['city_id']]]);
            $county = model('area')->getInfo([['id', '=', $order_detail['district_id']]]);
            $serviceCode = "EXP_RECE_CREATE_ORDER";
            $cargoDetails = $shunfeng_g;
            $contactInfoList = [];
            $contactInfoList[] = [
                "address" => $order_detail['address'],
                "city" => $city["name"],
                "contact" => $order_detail['name'],
                "contactType" => 2,
                "county" => $county["name"],
                "mobile" => $order_detail["mobile"],
                "province" => $province["name"]
            ];
            $contactInfoList[] = [
                "address" => "xxxxxxxxxxxxxxxxx",
                "city" => "聊城市",
                "contact" => "马福*",
                "county" => "东昌府区",
                "contactType" => 1,
                "mobile" => "16606345016",
                "company" => "xxxxxxxxxxx",
                "province" => "山东省"
            ];
            $msgData = [
                'cargoDetails' => $cargoDetails,
                'contactInfoList' => $contactInfoList,
                'expressTypeId' => 2,
                'language' => 'zh-CN',
                'monthlyCard' => '5313549825',
                'orderId' => time().'0001',
                'payMethod' => 1
            ];
            $msgData = json_encode($msgData);
            $shun = $order_common_model->shunfeng($serviceCode, $msgData);
            if ($shun['status'] == 1) {
                //保存快递编号
                $res = model("order")->update(['delivery_no' => $shun['waybillNo'], 'pdf' => ''], [['order_id', '=', $order_detail['order_id']]]);
                //测试顺丰下单
                $serviceCode = "COM_RECE_CLOUD_PRINT_WAYBILLS";
                $msgData = '{
    "templateCode": "fm_150_standard_QZKJPjIXU2Q",
    "version":"2.0",
    "fileType":"pdf",
    "documents": [{
    "masterWaybillNo": "' . $shun['waybillNo'] . '"
    }]
}';
                $order_common_model->shunfeng($serviceCode, $msgData);
            } else {
                return $this->error('无法生成运单'.$shun['msg']);
            }
            $this->error('请刷新页面重新生成运单');
        }else{
            header('Location:/'.$order_detail['pdf']);
        }

    }

    /**
     * 交易记录
     */
    public function tradelist()
    {
        $order_common_model = new OrderCommonModel();
        if (request()->isAjax()) {
            $page        = input('page', 1);
            $page_size   = input('page_size', PAGE_LIST_ROWS);
            $member_id   = input('member_id', 0);//会员id
            $search_text = input('search_text', 0);//h关键字查询
            $condition   = array();
            if ($member_id > 0) {
                $condition[] = ["member_id", "=", $member_id];
            }
            if (!empty($search_text)) {
                $condition[] = ['order_no|order_name', 'like', '%' . $search_text . '%'];
            }

            return $order_common_model->getTradePageList($condition, $page, $page_size, "create_time desc");

        }
    }

    /**
     * 订单关闭
     * @return mixed
     */
    public function delete()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            $order_common_model = new OrderCommonModel();
            $result             = $order_common_model->orderDelete($order_id);
            return $result;
        }
    }

    /**
     * 订单关闭
     * @return mixed
     */
    public function orderImage()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            $order_common_model = new OrderCommonModel();
            $result             = $order_common_model->orderImage($order_id);
            return $result;
        }
    }

    /**
     * 线下支付
     * @return array
     */
    public function offlinePay()
    {
        if (request()->isAjax()) {
            $order_id           = input("order_id", 0);
            //需要重新判断仓库库存是否足够
            $alias       = 'og';
            $join        = [
                [
                    'order_goods o',
                    'o.sku_id = og.sku_id',
                    'left'
                ]
            ];
            $goodslist = model('goods_sku')->getList([['o.order_id','=',$order_id]], 'og.*,o.num', 'og.sku_id desc', $alias, $join);
            foreach ($goodslist as $key =>$vo){
                if ($vo['stock']<1 || $vo['goods_state']==0){
                    return error('-1',$vo['sku_name'].'产品已下架');
                }
                //判断数量
                if ($vo['stock']<$vo['num']){
                    return error('-1',$vo['sku_name'].'产品库存不足');
                }
            }
            //余额2支付
            $pay_type='OFFLINE_PAY';
            $order_common_model = new OrderCommonModel();
            $order_detail_result = $order_common_model->getOrderDetail($order_id);
            $order_detail        = $order_detail_result["data"];
            if ($order_detail['admin_uid']!=$this->uid){
                return error('-1','该订单不是登录账号生成，不能支付，请刷新页面');
            }
            if ($order_detail['order_status']!=0){
                return error('-1','订单不在待支付状态，请刷新页面');
            }
            if ($order_detail['pay_status']>0){
                return error('-1','订单已支付成功,请不要重复支付');
            }
            $user_model = new UserModel();
            $user_bpdotocr = $user_model->bpdoctoruidList();
            $sysuser = model('user')->getInfo([['uid', "=", $this->uid]]);
            $balance_error = 1;
            if ($sysuser['isbalance']==1) {
                if ($user_bpdotocr[$this->uid]['bp']) {
                    $user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$this->uid]['bp']]]);
                    $order_detail['order_money'] = (($order_detail['delivery_money']+$order_detail['invoice_money']+$order_detail['promotion_money'])+round($user['balancediscount']*$order_detail['goods_money'],2));
                    if ($user['balance'] >= $order_detail['order_money']) {
                        //扣除余额
                        $dec_uid = $user_bpdotocr[$this->uid]['bp'];
                        $dec_money = $order_detail['order_money'];
                        //$res = model("user")->setDec([['uid', "=", $user_bpdotocr[$this->uid]['bp']]], "balance", $order_detail['order_money']);

                        $remar['uid'] = $user['uid'];
                        $remar['username'] = $user['username'];
                        $remar['user_uid_name'] = $sysuser['username'];
                        $remar['user_uid'] = $this->uid;
                        $remar['type'] = 1;
                        $remar['order_id'] = $order_detail["order_id"];
                        $remar['order_no'] = $order_detail["order_no"];
                        $remar['old_balance'] = $user["balance"];
                        $remar['new_balance'] = $user["balance"] - $order_detail['order_money'];
                        $remar['balance'] = $order_detail['order_money'];
                        $remar['fuwufei'] = ($order_detail['delivery_money']+$order_detail['invoice_money']+$order_detail['promotion_money']);
                        $remar['chengben'] = round($user['balancediscount']*$order_detail['goods_money'],2);
                        $remar['time'] = time();
                        $remar['remark'] = '订单' . $order_detail['order_no'] . ' 服务费:'.($order_detail['delivery_money']+$order_detail['invoice_money']+$order_detail['promotion_money']).' 产品费用:'.round($user['balancediscount']*$order_detail['goods_money'],2).' 折扣:'.$user['balancediscount'];
                        //model('user_balance_log')->add($remar);
                        $balance_error = 0;
                    }
                }
                if ($user_bpdotocr[$this->uid]['doctor'] && $balance_error == 1) {
                    $user = model('user')->getInfo([['uid', "=", $user_bpdotocr[$this->uid]['doctor']]]);
                    $order_detail['order_money'] = (($order_detail['delivery_money']+$order_detail['invoice_money']+$order_detail['promotion_money'])+round($user['balancediscount']*$order_detail['goods_money'],2));
                    if ($user['balance'] >= $order_detail['order_money']) {
                        //扣除余额
                        $dec_uid = $user_bpdotocr[$this->uid]['doctor'];
                        $dec_money = $order_detail['order_money'];
                        //$res = model("user")->setDec([['uid', "=", $user_bpdotocr[$this->uid]['doctor']]], "balance", $order_detail['order_money']);

                        $remar['uid'] = $user['uid'];
                        $remar['username'] = $user['username'];
                        $remar['user_uid_name'] = $sysuser['username'];
                        $remar['user_uid'] = $this->uid;
                        $remar['type'] = 1;
                        $remar['order_id'] = $order_detail["order_id"];
                        $remar['order_no'] = $order_detail["order_no"];
                        $remar['old_balance'] = $user["balance"];
                        $remar['new_balance'] = $user["balance"] - $order_detail['order_money'];
                        $remar['balance'] = $order_detail['order_money'];
                        $remar['fuwufei'] = ($order_detail['delivery_money']+$order_detail['invoice_money']+$order_detail['promotion_money']);
                        $remar['chengben'] = round($user['balancediscount']*$order_detail['goods_money'],2);
                        $remar['time'] = time();
                        $remar['remark'] = '订单' . $order_detail['order_no'] . ' 服务费:'.($order_detail['delivery_money']+$order_detail['invoice_money']+$order_detail['promotion_money']).' 产品费用:'.round($user['balancediscount']*$order_detail['goods_money'],2).' 折扣:'.$user['balancediscount'];
                        //model('user_balance_log')->add($remar);
                        $balance_error = 0;
                    }
                }
                if ($balance_error == 1) {
                    return error('-1', '账户余额不足，请及时联系产品经理充值');
                }
                $pay_type = 'BALANCE';
                //增加队列处理
                $queue_table = model('queue_table')->getInfo([['order_no', "=", $order_detail["order_no"]],['processed', "=", FALSE]]);
                if ($queue_table){
                    return;
                }else{
                    model('queue_table')->add(['order_no'=>$order_detail["order_no"]]);
                }
            }

            $order_common_model = new OrderCommonModel();
            $result             = $order_common_model->orderOfflinePay($order_id,$pay_type);
            if ($sysuser['isbalance']==1) {
                model("user")->setDec([['uid', "=", $dec_uid]], "balance", $dec_money);
                model('user_balance_log')->add($remar);
                model('queue_table')->update(['processed' => TRUE], [['order_no', '=', $order_detail["order_no"]]]);
            }
            //顺丰下单
            $order_common_model  = new OrderCommonModel();
            $order_detail_result = $order_common_model->getOrderDetail($order_id);
            $order_detail        = $order_detail_result["data"];
            $shunfeng_g[] = [
                "amount" => 100,
                "count" => 1,
                "name" => '保健品',
                "unit" => "瓶",
                "weight" => '20'
            ];
            //收货地址
            $province = model('area')->getInfo([['id', '=', $order_detail['province_id']]]);
            $city = model('area')->getInfo([['id', '=', $order_detail['city_id']]]);
            $county = model('area')->getInfo([['id', '=', $order_detail['district_id']]]);
            $serviceCode = "EXP_RECE_CREATE_ORDER";
            $cargoDetails = $shunfeng_g;
            $contactInfoList = [];
            $contactInfoList[] = [
                "address" => $order_detail['full_address'].$order_detail['address'],
                "city" => $city["name"],
                "contact" => $order_detail['name'],
                "contactType" => 2,
                "county" => $county["name"],
                "mobile" => $order_detail["mobile"],
                "province" => $province["name"]
            ];
            $contactInfoList[] = [
                "address" => "xxxxxxxxxxxxxxxxx",
                "city" => "聊城市",
                "contact" => "马福*",
                "county" => "东昌府区",
                "contactType" => 1,
                "mobile" => "16606345016",
                "company" => "xxxxxxxx",
                "province" => "山东省"
            ];
            $msgData = [
                'cargoDetails' => $cargoDetails,
                'contactInfoList' => $contactInfoList,
                'expressTypeId' => 2,
                'language' => 'zh-CN',
                'monthlyCard' => '5313549825',
                'orderId' => time().'0001',
                'payMethod' => 1
            ];
            $msgData = json_encode($msgData);
            $shun = $order_common_model->shunfeng($serviceCode, $msgData);
            if ($shun['status'] == 1) {
                //保存快递编号
                $res = model("order")->update(['delivery_no' => $shun['waybillNo'], 'pdf' => ''], [['order_id', '=', $order_detail['order_id']]]);
                 //测试顺丰下单
                $serviceCode = "COM_RECE_CLOUD_PRINT_WAYBILLS";
                $msgData = '{
              "templateCode": "fm_150_standard_QZKJPjIXU2Q",
              "version":"2.0",
              "fileType":"pdf",
              "documents": [{
              "masterWaybillNo": "' . $shun['waybillNo'] . '"
                }]
               }';
                $order_common_model->shunfeng($serviceCode, $msgData);
            }

            return $result;
        }
    }

    /**
     * 订单列表（发票）
     */
    public function invoiceOrderList()
    {
        if (request()->isAjax()) {
            $page_index = input('page', 1);
            $page_size  = input('page_size', PAGE_LIST_ROWS);
            $condition  = [
                ["site_id", "=", $this->site_id],
                ['is_delete', '=', 0],
                ['is_invoice', '=', 1]
            ];

            //订单编号
            $order_no = input('order_no', '');
            if ($order_no) {
                $condition[] = ["order_no", "like", "%" . $order_no . "%"];
            }
            //订单状态
            $order_status = input('order_status', '');
            if ($order_status != "") {
                $condition[] = ["order_status", "=", $order_status];
            }
            $order_type = input("order_type", 'all');//营销类型
            $start_time = input('start_time', '');
            $end_time   = input('end_time', '');


            //订单类型
            if ($order_type != 'all') {
                $condition[] = ["order_type", "=", $order_type];
            }

            if (!empty($start_time) && empty($end_time)) {
                $condition[] = ["create_time", ">=", date_to_time($start_time)];
            } elseif (empty($start_time) && !empty($end_time)) {
                $condition[] = ["create_time", "<=", date_to_time($end_time)];
            } elseif (!empty($start_time) && !empty($end_time)) {
                $condition[] = ['create_time', 'between', [date_to_time($start_time), date_to_time($end_time)]];
            }

            $order_common_model = new OrderCommonModel();
            $list               = $order_common_model->getOrderPageList($condition, $page_index, $page_size, "create_time desc");
            return $list;
        } else {
            $order_model       = new OrderModel();
            $order_status_list = $order_model->order_status;
            $this->assign("order_status_list", $order_status_list);//订单状态
            $order_common_model = new OrderCommonModel();
            $order_type_list    = $order_common_model->getOrderTypeStatusList();
            $this->assign("order_type_list", $order_type_list);

            return $this->fetch('order/invoice_list');
        }
    }
}