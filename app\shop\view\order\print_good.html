<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <script src="__STATIC__/js/jquery-3.1.1.js"></script>
    <script src="__STATIC__/ext/layui/layui.js"></script>
    <script>
        window.ns_url = {
            baseUrl: "ROOT_URL/",
            route: ['{:request()->module()}', '{:request()->controller()}', '{:request()->action()}'],
        };
    </script>
    <script type="text/javascript" src="__STATIC__/js/common.js" charset="utf-8"></script>
    <script type="text/javascript" src="__STATIC__/js/jquery.printarea.js" charset="utf-8"></script>
    <title>{$menu_info['title']|default="打印封口单"} - {$shop_info['site_name']|default=""}</title>
</head>
<body>
{notempty name="order_detail"}
<div class="print-layout">
    <!-- 屏幕端工具栏（不会参与打印） -->
    <div class="print-toolbar" style="position: fixed; top: 10px; right: 20px; z-index: 9999; display: flex; gap: 8px;">
        <button id="printbtn" type="button" style="padding: 6px 12px;">打印</button>
        <button id="toggle-preview" type="button" style="padding: 6px 12px;">关闭分页预览</button>
    </div>
    <div class="print-page">
        <div id="printarea">
            <!-- 内嵌样式，确保打印时不丢失 -->
            <style type="text/css">
                /* 基础样式 - 屏幕和打印通用 */
                body {
                    background: #FFF none !important;
                    font-family: "Arial", "Microsoft YaHei", "黑体", "宋体", sans-serif !important;
                    color: #000 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                /* 页面容器 */
                .w {
                    width: 210mm !important;
                    min-height: auto !important;
                    margin: 0 auto !important;
                    padding: 15mm !important;
                    box-sizing: border-box !important;
                    background: #FFF !important;
                    position: relative !important;
                }

                /* 页面头部信息区域 - 左对齐显示 */
                .page-header-info {
                    text-align: left !important;
                    font-size: 14px !important;
                    color: #000 !important;
                    line-height: 1.6 !important;
                }

                /* 主标题 */
                .main-title {
                    font-size: 48px !important;
                    font-weight: bold !important;
                    color: #000 !important;
                    letter-spacing: 2px !important;
                }

                /* 底部logo */
                .bottom-logo {
                    text-align: right !important;
                    margin-top: 20mm !important;
                    width: 40mm !important;
                    height: 10mm !important;
                    margin-left: auto !important;
                }

                .bottom-logo img {
                    width: 100% !important;
                    height: 100% !important;
                    object-fit: contain !important;
                }





                /* 时间段标题区域 */
                .time-section-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 5mm;
                    padding-bottom: 5mm;
                    border-bottom: 2px solid #000;
                }

                .time-section-title {
                    font-size: 24px;
                    font-weight: bold;
                    color: #000;
                }

                .daily-dosage {
                    font-size: 24px;
                    font-weight: bold;
                    color: #000;
                }

                /* 药品列表 */
                .medicine-list {
                    margin-bottom: 30mm;
                }

                .medicine-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 18px;
                    color: #000;
                    margin-bottom: 8mm;
                    line-height: 1.4;
                }

                .medicine-name {
                    flex: 1;
                }

                .medicine-quantity {
                    font-weight: bold;
                    min-width: 40px;
                    text-align: right;
                }

                /* 服用时间和生产日期 */
                .usage-info {
                    margin-top: 15mm;
                    font-size: 16px;
                    color: #000;
                    line-height: 1.8;
                }



                /* 分页样式 */
                .page {
                    page-break-after: always;
                    width: 100%;
                    height: 100%;
                    position: relative;
                }

                .page:last-child {
                    page-break-after: avoid;
                }

                /* 屏幕预览分页样式 */
                body.screen-paged .page {
                    border: 1px solid #ddd;
                    margin-bottom: 20px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }


                
                .production-date {
                    color: #000;
                    font-size: 16px;
                }
                
                .shrink2 {
                    text-indent: 2em;
                    font-size: 14px;
                    color: #000;
                }

                .noshrink {
                    line-height: 1.6em;
                }

                .shrink3 {
                    text-indent: 3em;
                    line-height: 1.6em;
                }

                .sectionbottom {
                    margin-bottom: 1.8em;
                }

                .textshrink {
                    margin-left: 20px;
                }

                .pagefot {
                    text-align: center;
                    font-family: "Arial", "Microsoft YaHei", "黑体", "宋体", sans-serif;
                    font-size: 12px;
                }

                .box1 {
                    /* width: 600px; */
                    width: 500px;
                    margin-left: -27px;
                    /*margin-top: 70px;*/
                }

                .box1>div {
                    width: 450px;
                    margin: 0 auto;
                }

                .box1 h1 {
                    text-align: center;
                    color: #1E1F1E;
                    font-size: 55px;
                }

                .box1 .tips {
                    color: #444443;
                    padding-top: 5px;
                    padding-bottom: 25px;
                    letter-spacing: 2.5px;
                }

                .box1 .arrange {
                    padding: 15px 10px;
                    border-radius: 15px;
                    margin-bottom: 60px;
                }

                .box1 .arrange .title {
                    color: #000;
                    font-weight: 600;
                    font-size: 28px;
                }

                .box1 .arrange .tag {
                    color: #000;
                    display: inline-block;
                    font-weight: 600;
                    height: 28px;
                    width: 270px;
                    margin-bottom: 10px;
                    font-size: 24px;
                }

                .box1 .details {
                    color: #000;
                    font-size: 30px;
                    font-weight: 600;
                    padding-bottom: 7px;
                }

                .directions {
                    padding-bottom: 58px;
                }

                .directions h1 {
                    text-align: left;
                }

                .directions .step {
                    overflow: hidden;
                    padding: 20px 0 25px 0;
                }

                .directions .step>div {
                    float: left;
                    width: 30.333333%;
                    padding: 7px;
                    text-align: center;
                }

                .directions .step img {
                    width: 100%;
                    padding-bottom: 15px;
                }

                .directions .step i {
                    font-style: normal;
                    font-size: 25px;
                    font-weight: 600;
                    color: #000;
                    padding-bottom: 15px;
                    display: inline-block;
                }

                .directions .step span {
                    display: inline-block;
                    width: 80%;
                    text-align: left;
                    color: #000;
                    font-size: 14px;
                }

                .directions .key {
                    color: #000;
                    font-size: 14px;
                    padding-bottom: 107px;
                }

                .notes .item {
                    background-color: #DBDADA;
                    border-radius: 15px;
                    padding: 15px 5px;
                }

                .notes .item ul {
                    padding: 0;
                    margin: 0;
                    list-style: none;
                }

                .notes .item ul li {
                    display: flex;
                    padding: 0 0 15px 0;
                    font-size: 14px;
                }

                .notes .item img {
                    width: 25px;
                    padding: 5px 15px 5px 5px;
                }
                
                /* 药品安排样式 */
                .medicine-arrange {
                    margin-top: 30px;
                    padding: 20px;
                    border: 3px solid #999;
                    border-radius: 10px;
                }
                
                .medicine-arrange .arrange-title {
                    color: #000;
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 15px;
                }
                
                .time-period {
                    margin-bottom: 20px;
                }
                
                .time-period-title {
                    color: #000;
                    font-size: 20px;
                    font-weight: 600;
                    margin-bottom: 8px;
                }
                
                .medicine-item {
                    color: #000;
                    font-size: 16px;
                    margin-bottom: 3px;
                }
                
                .service-time {
                    color: #000;
                    font-size: 16px;
                    margin-left: 0px;
                    margin-bottom: 5px;
                }
                
                .production-date {
                    color: #000;
                    font-size: 16px;
                    margin-left: 0px;
                    margin-bottom: 10px;
                }

                /* 打印媒体查询 - 确保打印时样式一致 */
                @media print {
                    /* 确保所有元素在打印时保持样式 */
                    * {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }

                    body {
                        background: #FFF none !important;
                        font-family: "Arial", "Microsoft YaHei", "黑体", "宋体", sans-serif !important;
                        color: #000 !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    .w {
                        width: 210mm !important;
                        min-height: auto !important;
                        margin: 0 auto !important;
                        padding: 15mm !important;
                        box-sizing: border-box !important;
                        background: #FFF !important;
                        position: relative !important;
                    }

                    .page-header-info {
                        text-align: left !important;
                        font-size: 14px !important;
                        color: #000 !important;
                        line-height: 1.6 !important;
                    }

                    .main-title {
                        font-size: 48px !important;
                        font-weight: bold !important;
                        color: #000 !important;
                        letter-spacing: 2px !important;
                    }

                    .bottom-logo {
                        text-align: right !important;
                        margin-top: 20mm !important;
                        width: 40mm !important;
                        height: 10mm !important;
                        margin-left: auto !important;
                    }

                    .bottom-logo img {
                        width: 100% !important;
                        height: 100% !important;
                        object-fit: contain !important;
                    }

                    .print-toolbar {
                        display: none !important;
                    }

                    .page {
                        page-break-after: always !important;
                        width: auto !important;
                        min-height: auto !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        border: none !important;
                        box-shadow: none !important;
                        background: white !important;
                    }

                    .page:last-child {
                        page-break-after: avoid !important;
                    }
                }
            </style>
            <div class="w">
                <div class="content">
                    <div class="box1" style="">
                        <div class="arrange">
                            <!-- 药品安排分页：每个非空餐包单独一页 -->
                            {notempty name="arrange_data"}
                            <style type="text/css">
                                .page {
                                    page-break-after: always;
                                    break-after: page;
                                }
                                .page:last-child {
                                    page-break-after: auto;
                                    break-after: auto;
                                }
                                /* 屏幕端显示为独立纸张，便于预览分页（通过 .screen-paged 开关） */
                                @media screen {
                                    .screen-paged .page {
                                        width: 210mm;              /* A4 宽度 */
                                        min-height: auto;          /* 自适应高度 */
                                        margin: 10mm auto;         /* 居中并上下留白 */
                                        padding: 15mm;             /* 与打印样式一致的内边距 */
                                        border: 1px solid #e5e5e5; /* 纸张边框 */
                                        background: #fff;
                                        box-shadow: 0 2mm 6mm rgba(0,0,0,0.08);
                                        box-sizing: border-box;    /* 确保尺寸计算一致 */
                                    }
                                    .screen-paged .page + .page { margin-top: 12mm; }
                                }

                                .page-header {
                                    display: flex;
                                    justify-content: space-between;
                                    align-items: center;
                                    font-size: 30px;
                                    font-weight: 600;
                                    margin-bottom: 12px;
                                    position: relative;
                                    padding-bottom: 8px;
                                    color: #000;
                                }
                                .page-header::after {
                                    content: '';
                                    position: absolute;
                                    left: 0;
                                    bottom: 0;
                                    width: 50%;
                                    border-bottom: 2px solid #000;
                                }
                            </style>

                            {php}
                            $__ordered = [];
                            $__slots__ = ['earlymoning','moning','aftnoon','canjian','night','sleep','evening'];
                            foreach ($__slots__ as $__s) {
                                if (isset($arrange_data[$__s]) && !empty($arrange_data[$__s]['goods'])) {
                                    $__ordered[] = $arrange_data[$__s];
                                }
                            }
                            {/php}

                            {volist name="__ordered" id="time_data"}
                            <div class="page">
                                <!-- 左上角信息 -->
                                <div class="page-header-info">
                                    <div>订单编号：GD123456789101</div>
                                    <div>生产日期：{$production_date|default="2025年08月13日"}</div>
                                    <div>服用时间：{$time_data.service_time|default="随餐服用"}</div>
                                </div>
                                
                                <!-- 主标题 -->
                                <div class="main-title">Hello! {$order_detail.name|default="芒果芝芝"}</div>
                                
                                <!-- 时间段标题 -->
                                <div class="time-section-header">
                                    <div class="time-section-title">{$time_data.name}：{$attr_class_info.day|default="30"}包</div>
                                    <div class="daily-dosage">每日剂量</div>
                                </div>
                                
                                <!-- 药品列表 -->
                                <div class="medicine-list">
                                    {volist name="time_data.goods" id="goods"}
                                    <div class="medicine-item">
                                        <div class="medicine-name">{$goods.sku_name}</div>
                                        <div class="medicine-quantity">×{$goods.num}</div>
                                    </div>
                                    {/volist}
                                </div>
                                
                                <!-- 底部logo -->
                                <div class="bottom-logo">
                                    <img src="__STATIC__/img/shop/printorderr_c_log.png" alt="个性化营养素">
                                </div>
                            </div>
                            {/volist}
                            {/notempty}

                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
{/notempty}
</body>
<script>
    $(function(){
        // 默认开启屏幕端分页预览
        $('body').addClass('screen-paged');

        // 打印按钮
        $("#printbtn").click(function(){
            $("#printarea").printArea();
        });

        // 分页预览开关
        $('#toggle-preview').on('click', function(){
            $('body').toggleClass('screen-paged');
            if ($('body').hasClass('screen-paged')) {
                $(this).text('关闭分页预览');
            } else {
                $(this).text('开启分页预览');
            }
        });
    });
</script>
</html>